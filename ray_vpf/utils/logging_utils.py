"""
Logging utilities for Ray VPF.
"""

import logging


def setup_logging(level: str = "INFO", format_string: str = None) -> None:
    """
    Setup logging configuration for Ray VPF.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        format_string: Custom format string for log messages
    """
    if format_string is None:
        format_string = "[%(levelname)s] %(asctime)s - %(name)s - %(message)s"
    
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    logging.basicConfig(
        level=numeric_level,
        format=format_string,
        datefmt='%H:%M:%S'
    )
    
    # Set specific logger levels
    logging.getLogger('ray_vpf').setLevel(numeric_level)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured at {level} level")
