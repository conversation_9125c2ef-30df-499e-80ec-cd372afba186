"""
Video file source implementation for Ray VPF.
"""

import os
import time
import queue
import threading
import logging
from typing import Optional, Tu<PERSON>, Dict, Any

import cv2
import numpy as np

from ..core import FrameSource

logger = logging.getLogger(__name__)


class VideoFileSource(FrameSource):
    """Production-ready video file source that emulates live camera behavior.

    Features:
    - Real-time frame pacing to match video FPS
    - Robust error handling and recovery
    - Resource management with context manager support
    - Thread-safe operations
    - Comprehensive logging and monitoring
    - Graceful degradation and fallback mechanisms
    - Loop playback support for continuous operation
    - Frame dropping/buffering strategies

    Args:
        path: Path to video file
        resize: Optional (width, height) tuple for frame resizing
        realtime: If True, pace frame delivery to match video FPS
        speed: Playback speed multiplier (1.0 = normal speed)
        loop: If True, restart video when it reaches the end
        max_retries: Maximum number of retry attempts for failed operations
        buffer_size: Internal frame buffer size for smooth delivery
        drop_frames: If True, drop frames when consumer is slow
    """

    def __init__(
        self,
        path: str,
        resize: Optional[Tuple[int, int]] = None,
        realtime: bool = False,
        speed: float = 1.0,
        loop: bool = False,
        max_retries: int = 3,
        buffer_size: int = 5,
        drop_frames: bool = True
    ):
        # Input validation
        self._validate_inputs(path, resize, speed, max_retries, buffer_size)

        # Core configuration
        self.path = path
        self.resize = resize
        self.realtime = bool(realtime)
        self.speed = max(0.1, min(10.0, float(speed)))  # Clamp speed to reasonable range
        self.loop = bool(loop)
        self.max_retries = max(1, int(max_retries))
        self.buffer_size = max(1, int(buffer_size))
        self.drop_frames = bool(drop_frames)

        # Internal state
        self._cap: Optional[cv2.VideoCapture] = None
        self._stopped = True
        self._frame_interval: Optional[float] = None
        self._next_frame_time: Optional[float] = None
        self._lock = threading.RLock()  # Thread safety
        self._eof_reached = False  # Track end-of-file state

        # Video properties (cached after opening)
        self._fps: Optional[float] = None
        self._frame_count: Optional[int] = None
        self._current_frame: int = 0

        # Error handling and monitoring
        self._retry_count = 0
        self._error_count = 0
        self._frames_read = 0
        self._start_time: Optional[float] = None

        # Frame buffer for smooth delivery
        self._frame_buffer: queue.Queue = queue.Queue(maxsize=self.buffer_size)
        self._buffer_thread: Optional[threading.Thread] = None
        self._buffer_stop_event = threading.Event()

    def _validate_inputs(self, path: str, resize: Optional[Tuple[int, int]],
                        speed: float, max_retries: int, buffer_size: int) -> None:
        """Validate constructor inputs."""
        if not isinstance(path, str) or not path.strip():
            raise ValueError("Path must be a non-empty string")

        if resize is not None:
            if not isinstance(resize, (tuple, list)) or len(resize) != 2:
                raise ValueError("Resize must be a tuple of (width, height)")
            if not all(isinstance(x, int) and x > 0 for x in resize):
                raise ValueError("Resize dimensions must be positive integers")

        if not isinstance(speed, (int, float)) or speed <= 0:
            raise ValueError("Speed must be a positive number")

        if not isinstance(max_retries, int) or max_retries < 1:
            raise ValueError("Max retries must be a positive integer")

        if not isinstance(buffer_size, int) or buffer_size < 1:
            raise ValueError("Buffer size must be a positive integer")

    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()
        return False  # Don't suppress exceptions

    @property
    def is_opened(self) -> bool:
        """Check if video source is currently opened."""
        with self._lock:
            return self._cap is not None and self._cap.isOpened()

    @property
    def fps(self) -> Optional[float]:
        """Get video FPS."""
        return self._fps

    @property
    def frame_count(self) -> Optional[int]:
        """Get total frame count."""
        return self._frame_count

    @property
    def current_frame(self) -> int:
        """Get current frame number."""
        return self._current_frame

    @property
    def progress(self) -> float:
        """Get playback progress (0.0 to 1.0)."""
        if self._frame_count and self._frame_count > 0:
            return min(1.0, self._current_frame / self._frame_count)
        return 0.0

    def get_stats(self) -> Dict[str, Any]:
        """Get operational statistics."""
        with self._lock:
            elapsed = time.time() - self._start_time if self._start_time else 0
            effective_fps = self._frames_read / elapsed if elapsed > 0 else 0

            return {
                'frames_read': self._frames_read,
                'current_frame': self._current_frame,
                'progress': self.progress,
                'error_count': self._error_count,
                'retry_count': self._retry_count,
                'effective_fps': effective_fps,
                'buffer_size': self._frame_buffer.qsize(),
                'is_opened': self.is_opened,
                'elapsed_time': elapsed
            }

    def start(self) -> None:
        """Start the video source with robust error handling."""
        with self._lock:
            if not self._stopped:
                logger.warning("VideoFileSource already started")
                return

            self._start_time = time.time()
            self._stopped = False
            self._retry_count = 0
            self._eof_reached = False  # Reset EOF flag

            # Attempt to open video with retries
            for attempt in range(self.max_retries):
                try:
                    self._open_video()
                    break
                except Exception as e:
                    self._retry_count += 1
                    logger.warning(f"Failed to open video (attempt {attempt + 1}/{self.max_retries}): {e}")
                    if attempt == self.max_retries - 1:
                        raise RuntimeError(f"Failed to open video file after {self.max_retries} attempts: {self.path}")
                    time.sleep(0.5 * (attempt + 1))  # Exponential backoff

            # Start buffer thread if using buffered mode
            if self.buffer_size > 1:
                self._buffer_stop_event.clear()
                self._buffer_thread = threading.Thread(target=self._buffer_loop, daemon=True)
                self._buffer_thread.start()

            logger.info(
                "VideoFileSource started: %s (realtime=%s, fps=%.3f, loop=%s, buffer_size=%d)",
                self.path, self.realtime, self._fps, self.loop, self.buffer_size
            )

    def _open_video(self) -> None:
        """Open video file and cache properties."""
        # Validate file exists
        if not os.path.isfile(self.path):
            raise FileNotFoundError(f"Video file not found: {self.path}")

        # Open video capture
        self._cap = cv2.VideoCapture(self.path)
        if not self._cap.isOpened():
            raise RuntimeError(f"OpenCV failed to open video file: {self.path}")

        # Cache video properties
        self._fps = self._cap.get(cv2.CAP_PROP_FPS) or 30.0
        if self._fps <= 1e-3:
            self._fps = 30.0
            logger.warning("Invalid FPS detected, using default 30.0")

        self._frame_count = int(self._cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if self._frame_count <= 0:
            logger.warning("Could not determine frame count")

        # Calculate frame interval for real-time pacing
        effective_fps = self._fps * self.speed
        self._frame_interval = 1.0 / effective_fps
        self._next_frame_time = None
        self._current_frame = 0

        logger.debug(
            "Video opened: fps=%.3f, frames=%d, duration=%.2fs",
            self._fps, self._frame_count,
            self._frame_count / self._fps if self._fps > 0 else 0
        )

    def _buffer_loop(self) -> None:
        """Background thread for frame buffering."""
        logger.debug("Frame buffer thread started")

        while not self._buffer_stop_event.is_set():
            try:
                if self._frame_buffer.qsize() < self.buffer_size:
                    frame = self._read_frame_internal()
                    if frame is not None:
                        try:
                            self._frame_buffer.put_nowait(frame)
                        except queue.Full:
                            if self.drop_frames:
                                # Drop oldest frame
                                try:
                                    self._frame_buffer.get_nowait()
                                    self._frame_buffer.put_nowait(frame)
                                except queue.Empty:
                                    pass
                    else:
                        # End of video
                        if self.loop:
                            self._restart_video()
                        else:
                            # Signal EOF and stop buffering
                            with self._lock:
                                self._eof_reached = True
                            logger.debug("EOF reached, buffer thread stopping")
                            break
                else:
                    time.sleep(0.001)  # Small sleep when buffer is full

            except Exception as e:
                logger.error(f"Buffer thread error: {e}")
                self._error_count += 1
                time.sleep(0.1)

        logger.debug("Frame buffer thread stopped")

    def _restart_video(self) -> None:
        """Restart video from beginning for loop playback."""
        try:
            if self._cap and self._cap.isOpened():
                self._cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                self._current_frame = 0
                self._next_frame_time = None
                with self._lock:
                    self._eof_reached = False  # Reset EOF flag on restart
                logger.debug("Video restarted for loop playback")
        except Exception as e:
            logger.error(f"Failed to restart video: {e}")
            self._error_count += 1

    def read(self) -> Optional[np.ndarray]:
        """Read next frame with robust error handling and real-time pacing."""
        if self._stopped:
            return None

        # Check if EOF has been reached
        with self._lock:
            if self._eof_reached and self._frame_buffer.empty():
                logger.debug("EOF reached and buffer empty, returning None")
                return None

        try:
            # Use buffered read if buffer thread is active
            if self._buffer_thread and self._buffer_thread.is_alive():
                return self._read_from_buffer()
            else:
                return self._read_frame_direct()

        except Exception as e:
            logger.error(f"Frame read error: {e}")
            self._error_count += 1
            return None

    def _read_from_buffer(self) -> Optional[np.ndarray]:
        """Read frame from internal buffer."""
        try:
            frame = self._frame_buffer.get(timeout=0.1)
            self._apply_realtime_pacing()
            return frame
        except queue.Empty:
            # Buffer empty, check if EOF reached
            with self._lock:
                if self._eof_reached:
                    logger.debug("Buffer empty and EOF reached")
                    return None
            # Buffer empty but not EOF, try direct read as fallback
            return self._read_frame_direct()

    def _read_frame_direct(self) -> Optional[np.ndarray]:
        """Read frame directly from video source."""
        frame = self._read_frame_internal()
        if frame is not None:
            self._apply_realtime_pacing()
        elif self.loop:
            # Try to restart for loop playback
            self._restart_video()
            frame = self._read_frame_internal()
            if frame is not None:
                self._apply_realtime_pacing()
        else:
            # EOF reached and not looping
            with self._lock:
                self._eof_reached = True
            logger.debug("EOF reached in direct read")

        return frame

    def _read_frame_internal(self) -> Optional[np.ndarray]:
        """Internal frame reading with error handling."""
        with self._lock:
            if not self.is_opened:
                return None

            try:
                ret, frame_bgr = self._cap.read()
                if not ret:
                    return None

                # Update frame counter
                self._current_frame += 1
                self._frames_read += 1

                # Apply resize if specified
                if self.resize is not None:
                    frame_bgr = cv2.resize(frame_bgr, self.resize)

                # Convert to RGB
                frame_rgb = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)
                return frame_rgb

            except Exception as e:
                logger.error(f"Internal frame read error: {e}")
                self._error_count += 1
                return None

    def _apply_realtime_pacing(self) -> None:
        """Apply real-time frame pacing to match video FPS."""
        if not self.realtime or self._frame_interval is None:
            return

        now = time.monotonic()
        if self._next_frame_time is None:
            # First frame: schedule next time
            self._next_frame_time = now + self._frame_interval
        else:
            # Sleep until scheduled time if we're early
            to_wait = self._next_frame_time - now
            if to_wait > 0:
                time.sleep(to_wait)
                # Schedule next frame relative to previous schedule
                self._next_frame_time += self._frame_interval
            else:
                # We're late; advance next_frame_time to avoid drift
                self._next_frame_time = now + self._frame_interval

    def stop(self) -> None:
        """Stop the video source and clean up resources."""
        with self._lock:
            if self._stopped:
                return

            self._stopped = True

            # Stop buffer thread
            if self._buffer_thread:
                self._buffer_stop_event.set()
                try:
                    self._buffer_thread.join(timeout=2.0)
                except Exception:
                    pass
                self._buffer_thread = None

            # Clear buffer
            while not self._frame_buffer.empty():
                try:
                    self._frame_buffer.get_nowait()
                except queue.Empty:
                    break

            # Release video capture
            if self._cap is not None:
                try:
                    self._cap.release()
                except Exception as e:
                    logger.warning(f"Error releasing video capture: {e}")
                finally:
                    self._cap = None

            # Log final statistics
            stats = self.get_stats()
            logger.info(
                "VideoFileSource stopped: frames_read=%d, errors=%d, effective_fps=%.2f",
                stats['frames_read'], stats['error_count'], stats['effective_fps']
            )

    def seek(self, frame_number: int) -> bool:
        """Seek to specific frame number."""
        with self._lock:
            if not self.is_opened or frame_number < 0:
                return False

            try:
                if self._frame_count and frame_number >= self._frame_count:
                    frame_number = self._frame_count - 1

                self._cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                self._current_frame = frame_number
                self._next_frame_time = None  # Reset timing
                return True

            except Exception as e:
                logger.error(f"Seek error: {e}")
                self._error_count += 1
                return False

    def seek_time(self, seconds: float) -> bool:
        """Seek to specific time position."""
        if self._fps and self._fps > 0:
            frame_number = int(seconds * self._fps)
            return self.seek(frame_number)
        return False

    def reset(self) -> bool:
        """Reset video to beginning."""
        return self.seek(0)

    def set_speed(self, speed: float) -> None:
        """Change playback speed dynamically."""
        with self._lock:
            self.speed = max(0.1, min(10.0, float(speed)))
            if self._fps:
                effective_fps = self._fps * self.speed
                self._frame_interval = 1.0 / effective_fps
                self._next_frame_time = None  # Reset timing
                logger.debug(f"Speed changed to {self.speed}x (effective fps: {effective_fps:.2f})")

    def get_duration(self) -> Optional[float]:
        """Get video duration in seconds."""
        if self._fps and self._frame_count and self._fps > 0:
            return self._frame_count / self._fps
        return None

    def get_remaining_time(self) -> Optional[float]:
        """Get remaining playback time in seconds."""
        duration = self.get_duration()
        if duration and self._fps and self._fps > 0:
            current_time = self._current_frame / self._fps
            return max(0.0, duration - current_time)
        return None

    def is_live_like(self) -> bool:
        """Check if source is configured to behave like a live camera."""
        return self.realtime and not self.loop

    def __str__(self) -> str:
        """String representation."""
        return f"VideoFileSource(path='{self.path}', realtime={self.realtime}, loop={self.loop})"

    def __repr__(self) -> str:
        """Detailed string representation."""
        return (
            f"VideoFileSource(path='{self.path}', resize={self.resize}, "
            f"realtime={self.realtime}, speed={self.speed}, loop={self.loop}, "
            f"buffer_size={self.buffer_size}, fps={self._fps})"
        )
