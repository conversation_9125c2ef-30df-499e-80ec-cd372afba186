#!/usr/bin/env python3
"""
Quick test to verify live camera is working with Ray VPF.
"""

import sys
import time
import signal

# Add ray_vpf to path
sys.path.insert(0, '.')

from ray_vpf import (
    Picamera2Source,
    create_face_detection_model,
    create_face_tracking_processor_preset,
    setup_logging
)

def signal_handler(sig, frame):
    print('\nStopping camera test...')
    sys.exit(0)

def main():
    # Setup logging
    setup_logging("INFO")
    
    # Setup signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    print("="*60)
    print("RAY VPF LIVE CAMERA TEST")
    print("="*60)
    
    try:
        # Create camera source
        print("Creating Picamera2 source...")
        source = Picamera2Source(size=(640, 480), format="RGB888")
        source.start()
        print("✓ Camera started successfully!")
        
        # Create face detection model
        print("Creating face detection model...")
        model = create_face_detection_model(model_type='auto')
        print("✓ Face detection model loaded!")
        
        # Create processor
        print("Creating face tracking processor...")
        processor = create_face_tracking_processor_preset('demo')
        print("✓ Face tracking processor created!")
        
        print("\nTesting live camera feed...")
        print("Press Ctrl+C to stop")
        print("-" * 60)
        
        frame_count = 0
        start_time = time.time()
        
        # Test reading frames for 10 seconds
        while frame_count < 100:  # Limit to 100 frames
            # Read frame
            frame = source.read()
            if frame is None:
                print("No frame received")
                break
            
            frame_count += 1
            
            # Run face detection every 10 frames to avoid overload
            if frame_count % 10 == 0:
                try:
                    # Run inference
                    model_out = model.infer(frame)
                    
                    # Create frame packet
                    from ray_vpf.core import FramePacket
                    pkt = FramePacket(
                        frame_rgb=frame,
                        model_out=model_out,
                        timestamp=time.time(),
                        frame_idx=frame_count,
                        meta={}
                    )
                    
                    # Process frame
                    processed_frame = processor.process(pkt)
                    
                    # Get statistics
                    stats = processor.get_statistics()
                    
                    elapsed = time.time() - start_time
                    fps = frame_count / elapsed if elapsed > 0 else 0
                    
                    print(f"Frame {frame_count:3d}: {frame.shape} | "
                          f"FPS: {fps:.1f} | "
                          f"Faces: {stats.get('active_tracks', 0)} | "
                          f"Unique: {stats.get('unique_faces_seen', 0)}")
                    
                except Exception as e:
                    print(f"Processing error: {e}")
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.05)  # 20 FPS max
        
        # Final statistics
        elapsed = time.time() - start_time
        avg_fps = frame_count / elapsed if elapsed > 0 else 0
        final_stats = processor.get_statistics()
        
        print("-" * 60)
        print("LIVE CAMERA TEST COMPLETED")
        print("-" * 60)
        print(f"Total Frames: {frame_count}")
        print(f"Total Time: {elapsed:.1f}s")
        print(f"Average FPS: {avg_fps:.1f}")
        print(f"Faces Detected: {final_stats.get('unique_faces_seen', 0)}")
        print(f"Active Tracks: {final_stats.get('active_tracks', 0)}")
        print("✓ Live camera test successful!")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Cleanup
        try:
            source.stop()
            print("Camera stopped.")
        except:
            pass
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
