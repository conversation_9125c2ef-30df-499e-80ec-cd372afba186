"""
Utility functions and factory classes for Ray VPF processors.
"""

from typing import Dict, List, Optional, Tu<PERSON>, Any

from ..core import InferenceModel
from .supervision_integration import (
    SupervisionConverter,
    SupervisionTracker,
    EnhancedFrameProcessor,
    SUPERVISION_AVAILABLE
)
from .advanced_processors import AdvancedFaceTrackingProcessor

# Check supervision availability
if SUPERVISION_AVAILABLE:
    import supervision as sv


class DetectionSourceFactory:
    """
    Factory class for creating detection sources and processors with supervision integration.

    This factory simplifies the creation of detection processing pipelines by providing
    pre-configured components that work seamlessly together.
    """

    @staticmethod
    def create_enhanced_processor(
        conf_threshold: float = 0.0,
        enable_supervision: bool = True,
        enable_tracking: bool = False,
        tracker_type: str = 'bytetrack',
        tracking_params: Optional[Dict[str, Any]] = None
    ) -> EnhancedFrameProcessor:
        """
        Create an enhanced frame processor with supervision and tracking capabilities.

        Args:
            conf_threshold: Confidence threshold for detections
            enable_supervision: Whether to enable supervision integration
            enable_tracking: Whether to enable object tracking
            tracker_type: Type of tracker to use
            tracking_params: Additional tracking parameters

        Returns:
            Configured EnhancedFrameProcessor instance
        """
        tracker_config = {'tracker_type': tracker_type}
        if tracking_params:
            tracker_config.update(tracking_params)

        return EnhancedFrameProcessor(
            conf_threshold=conf_threshold,
            enable_supervision=enable_supervision,
            enable_tracking=enable_tracking,
            tracker_config=tracker_config
        )

    @staticmethod
    def create_supervision_converter(enable_caching: bool = True) -> SupervisionConverter:
        """Create a supervision converter with optimal settings."""
        return SupervisionConverter(enable_caching=enable_caching)

    @staticmethod
    def create_tracker(
        tracker_type: str = 'bytetrack',
        sensitivity: str = 'medium'
    ) -> SupervisionTracker:
        """
        Create a tracker with predefined sensitivity settings.

        Args:
            tracker_type: Type of tracker ('bytetrack')
            sensitivity: Sensitivity level ('low', 'medium', 'high')

        Returns:
            Configured SupervisionTracker instance
        """
        # Predefined sensitivity configurations
        sensitivity_configs = {
            'low': {
                'track_activation_threshold': 0.5,
                'lost_track_buffer': 15,
                'minimum_matching_threshold': 0.9
            },
            'medium': {
                'track_activation_threshold': 0.25,
                'lost_track_buffer': 30,
                'minimum_matching_threshold': 0.8
            },
            'high': {
                'track_activation_threshold': 0.1,
                'lost_track_buffer': 50,
                'minimum_matching_threshold': 0.7
            }
        }

        config = sensitivity_configs.get(sensitivity, sensitivity_configs['medium'])
        config['tracker_type'] = tracker_type

        return SupervisionTracker(**config)


def create_hailo_showcase_processor(
    conf_threshold: float = 0.0,
    enable_tracking: bool = False,
    tracking_sensitivity: str = 'medium'
) -> EnhancedFrameProcessor:
    """
    Convenience function to create a processor optimized for Hailo detections.

    Args:
        conf_threshold: Confidence threshold for detections
        enable_tracking: Whether to enable object tracking
        tracking_sensitivity: Tracking sensitivity ('low', 'medium', 'high')

    Returns:
        Configured processor for Hailo detection workflows
    """
    tracking_params = None
    if enable_tracking:
        sensitivity_configs = {
            'low': {'track_activation_threshold': 0.5, 'lost_track_buffer': 15},
            'medium': {'track_activation_threshold': 0.25, 'lost_track_buffer': 30},
            'high': {'track_activation_threshold': 0.1, 'lost_track_buffer': 50}
        }
        tracking_params = sensitivity_configs.get(tracking_sensitivity, sensitivity_configs['medium'])

    return DetectionSourceFactory.create_enhanced_processor(
        conf_threshold=conf_threshold,
        enable_supervision=True,
        enable_tracking=enable_tracking,
        tracking_params=tracking_params
    )


def create_face_tracking_processor(
    conf_threshold: float = 0.5,
    tracking_sensitivity: str = 'medium',
    show_trails: bool = True,
    show_stats: bool = True,
    trail_length: int = 30,
    enable_tracking: bool = True,
    stats_position: str = 'top-left'
) -> AdvancedFaceTrackingProcessor:
    """
    Convenience function to create an advanced face tracking processor.

    Args:
        conf_threshold: Confidence threshold for face detections
        tracking_sensitivity: Tracking sensitivity ('low', 'medium', 'high')
        show_trails: Whether to show tracking trails
        show_stats: Whether to show statistics overlay
        trail_length: Maximum length of tracking trails
        enable_tracking: Whether to enable face tracking
        stats_position: Position for stats overlay

    Returns:
        Configured AdvancedFaceTrackingProcessor for face tracking workflows
    """
    return AdvancedFaceTrackingProcessor(
        conf_threshold=conf_threshold,
        tracking_sensitivity=tracking_sensitivity,
        show_trails=show_trails,
        show_stats=show_stats,
        trail_length=trail_length,
        enable_tracking=enable_tracking,
        stats_position=stats_position
    )


def create_face_tracking_processor_preset(preset: str = 'balanced') -> AdvancedFaceTrackingProcessor:
    """
    Create face tracking processor with predefined presets.

    Args:
        preset: Preset configuration ('performance', 'balanced', 'quality', 'demo')

    Returns:
        Configured AdvancedFaceTrackingProcessor
    """
    presets = {
        'performance': {
            'conf_threshold': 0.7,
            'tracking_sensitivity': 'low',
            'show_trails': False,
            'show_stats': True,
            'trail_length': 15,
            'stats_position': 'top-left'
        },
        'balanced': {
            'conf_threshold': 0.5,
            'tracking_sensitivity': 'medium',
            'show_trails': True,
            'show_stats': True,
            'trail_length': 30,
            'stats_position': 'top-left'
        },
        'quality': {
            'conf_threshold': 0.3,
            'tracking_sensitivity': 'high',
            'show_trails': True,
            'show_stats': True,
            'trail_length': 50,
            'stats_position': 'top-left'
        },
        'demo': {
            'conf_threshold': 0.4,
            'tracking_sensitivity': 'medium',
            'show_trails': True,
            'show_stats': True,
            'trail_length': 40,
            'stats_position': 'top-left',
            'fade_effect': True,
            'trail_thickness': 3
        }
    }

    config = presets.get(preset, presets['balanced'])
    return AdvancedFaceTrackingProcessor(**config)


def convert_detections_to_supervision(
    detections: Any,
    source_format: str = 'hailo',
    frame_shape: Optional[Tuple[int, int]] = None,
    class_names: Optional[List[str]] = None
) -> Optional["sv.Detections"]:
    """
    Convenience function for converting detections to supervision format.

    Args:
        detections: Detection results in various formats
        source_format: Source format ('hailo', 'yolo', 'generic', 'auto')
        frame_shape: (height, width) for coordinate normalization
        class_names: Optional class names for detections

    Returns:
        supervision.Detections object or None
    """
    converter = SupervisionConverter()
    return converter.convert_to_supervision(
        detections=detections,
        source_format=source_format,
        frame_shape=frame_shape,
        class_names=class_names
    )


def create_tracking_workflow(
    model: InferenceModel,
    processor_class: type = EnhancedFrameProcessor,
    tracking_sensitivity: str = 'medium',
    conf_threshold: float = 0.25
) -> Tuple[InferenceModel, EnhancedFrameProcessor]:
    """
    Create a complete tracking workflow with model and processor.

    Args:
        model: Inference model instance
        processor_class: Processor class to use
        tracking_sensitivity: Tracking sensitivity level
        conf_threshold: Detection confidence threshold

    Returns:
        Tuple of (model, processor) ready for tracking workflow
    """
    processor = processor_class(
        conf_threshold=conf_threshold,
        enable_supervision=True,
        enable_tracking=True,
        tracker_config={'tracker_type': 'bytetrack'}
    )

    return model, processor
