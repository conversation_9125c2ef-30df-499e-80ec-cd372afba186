"""
Degirum model implementations for Ray VPF.
"""

import os
import logging
from typing import Optional, Any

import numpy as np

from ..core import InferenceModel

logger = logging.getLogger(__name__)


class DegirumRetinaFaceModel(InferenceModel):
    """
    Degirum RetinaFace model adapter for the showcase framework.
    Requires `degirum` package and a local model zoo path.

    Usage:
        model = DegirumRetinaFaceModel(
            model_name="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
            zoo_path="~/degirum-zoo"
        )
    """

    def __init__(self, model_name: str, zoo_path: Optional[str] = None):
        try:
            import degirum as dg
        except ImportError:
            raise RuntimeError(
                "The 'degirum' package is not installed. Install it or use DummyModel for testing."
            )

        self.dg = dg
        self.model_name = model_name
        self.zoo_path = os.path.expanduser(zoo_path) if zoo_path else None
        self.zoo = None
        self.model = None

    def load(self) -> None:
        """Load the Degirum model."""
        logger.info("Connecting to Degirum local zoo: %s", self.zoo_path)
        self.zoo = self.dg.connect(self.dg.LOCAL, self.zoo_path)
        self.model = self.zoo.load_model(self.model_name)
        try:
            # Some runtimes accept this hint for correct color ordering
            self.model.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        logger.info("Degirum model loaded: %s", self.model_name)

    def infer(self, frame_rgb: np.ndarray) -> Any:
        """Run inference on a frame."""
        if self.model is None:
            self.load()
        return self.model(frame_rgb)

    def warmup(self, frame_rgb: np.ndarray) -> None:
        """Optional warmup to prime the accelerator."""
        if self.model is None:
            self.load()
        try:
            _ = self.model(frame_rgb)
        except Exception as e:
            logger.warning("Degirum warmup failed: %s", e)
