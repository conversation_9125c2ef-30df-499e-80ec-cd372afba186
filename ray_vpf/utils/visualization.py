"""
Visualization utilities for Ray VPF.
"""

import cv2
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Optional, Any

logger = logging.getLogger(__name__)


class StatsOverlay:
    """
    Professional statistics overlay for video frames.
    
    Provides customizable statistics display with multiple positioning options,
    background styling, and performance optimization.
    """
    
    def __init__(
        self,
        position: str = 'top-left',
        background_alpha: float = 0.7,
        text_color: Tuple[int, int, int] = (255, 255, 255),
        background_color: Tuple[int, int, int] = (0, 0, 0),
        font_scale: float = 0.5,
        font_thickness: int = 1,
        padding: int = 10,
        line_spacing: int = 25
    ):
        """
        Initialize the statistics overlay.
        
        Args:
            position: Position for overlay ('top-left', 'top-right', 'bottom-left', 'bottom-right')
            background_alpha: Alpha value for background transparency
            text_color: RGB color for text
            background_color: RGB color for background
            font_scale: Font scale factor
            font_thickness: Font thickness
            padding: Padding around text
            line_spacing: Spacing between lines
        """
        self.position = position
        self.background_alpha = background_alpha
        self.text_color = text_color
        self.background_color = background_color
        self.font_scale = font_scale
        self.font_thickness = font_thickness
        self.padding = padding
        self.line_spacing = line_spacing
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        
    def draw_stats(self, frame: np.ndarray, stats: Dict[str, Any]) -> np.ndarray:
        """
        Draw statistics overlay on frame.
        
        Args:
            frame: Input frame
            stats: Statistics dictionary
            
        Returns:
            Frame with statistics overlay
        """
        if not stats:
            return frame
            
        # Prepare statistics text lines
        stats_lines = self._format_stats(stats)
        
        if not stats_lines:
            return frame
            
        # Calculate overlay dimensions
        h, w = frame.shape[:2]
        overlay_width = 400
        overlay_height = len(stats_lines) * self.line_spacing + 2 * self.padding
        
        # Calculate position
        x, y = self._calculate_position(w, h, overlay_width, overlay_height)
        
        # Draw background
        overlay = frame.copy()
        cv2.rectangle(
            overlay, 
            (x, y), 
            (x + overlay_width, y + overlay_height), 
            self.background_color, 
            -1
        )
        frame = cv2.addWeighted(frame, self.background_alpha, overlay, 1 - self.background_alpha, 0)
        
        # Draw text lines
        for i, line in enumerate(stats_lines):
            text_y = y + self.padding + (i + 1) * self.line_spacing
            cv2.putText(
                frame, 
                line, 
                (x + self.padding, text_y), 
                self.font, 
                self.font_scale, 
                self.text_color, 
                self.font_thickness
            )
        
        return frame
    
    def _format_stats(self, stats: Dict[str, Any]) -> List[str]:
        """Format statistics into display lines."""
        lines = []
        
        # FPS information
        if 'current_fps' in stats and 'avg_fps' in stats:
            lines.append(f"FPS: {stats['current_fps']:.1f} (Avg: {stats['avg_fps']:.1f})")
        
        # Detection information
        if 'active_tracks' in stats:
            faces_count = stats.get('current_detections', 0)
            lines.append(f"Faces: {faces_count} | Active Tracks: {stats['active_tracks']}")
        
        # Frame information
        if 'total_frames' in stats and 'frames_with_faces' in stats:
            lines.append(f"Frames: {stats['total_frames']} | With Faces: {stats['frames_with_faces']}")
        
        # Tracking information
        if 'unique_faces_seen' in stats and 'max_concurrent_faces' in stats:
            lines.append(f"Unique: {stats['unique_faces_seen']} | Max Concurrent: {stats['max_concurrent_faces']}")
        
        # Detection rate
        if 'face_detection_rate' in stats:
            lines.append(f"Detection Rate: {stats['face_detection_rate']:.1%}")
        
        # Runtime
        if 'runtime' in stats:
            lines.append(f"Runtime: {stats['runtime']:.1f}s")
        
        # Error information
        if 'processing_errors' in stats and stats['processing_errors'] > 0:
            lines.append(f"Errors: {stats['processing_errors']}")
        
        return lines
    
    def _calculate_position(self, frame_w: int, frame_h: int, overlay_w: int, overlay_h: int) -> Tuple[int, int]:
        """Calculate overlay position based on configuration."""
        margin = 10
        
        if self.position == 'top-left':
            return margin, margin
        elif self.position == 'top-right':
            return frame_w - overlay_w - margin, margin
        elif self.position == 'bottom-left':
            return margin, frame_h - overlay_h - margin
        elif self.position == 'bottom-right':
            return frame_w - overlay_w - margin, frame_h - overlay_h - margin
        else:
            # Default to top-left
            return margin, margin


class FrameAnnotator:
    """
    Enhanced frame annotator with professional styling.
    
    Provides advanced annotation capabilities including bounding boxes,
    labels, confidence scores, and tracking information.
    """
    
    def __init__(
        self,
        box_thickness: int = 2,
        label_font_scale: float = 0.6,
        label_thickness: int = 1,
        label_padding: int = 5,
        confidence_precision: int = 2
    ):
        """
        Initialize the frame annotator.
        
        Args:
            box_thickness: Thickness of bounding box lines
            label_font_scale: Font scale for labels
            label_thickness: Font thickness for labels
            label_padding: Padding around labels
            confidence_precision: Decimal precision for confidence scores
        """
        self.box_thickness = box_thickness
        self.label_font_scale = label_font_scale
        self.label_thickness = label_thickness
        self.label_padding = label_padding
        self.confidence_precision = confidence_precision
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        
        # Color palette for different tracks
        self.colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
            (255, 0, 255), (0, 255, 255), (255, 128, 0), (128, 255, 0),
            (255, 0, 128), (128, 0, 255), (0, 128, 255), (0, 255, 128)
        ]
    
    def annotate_detections(
        self, 
        frame: np.ndarray, 
        detections: List[Dict[str, Any]],
        show_confidence: bool = True,
        show_labels: bool = True
    ) -> np.ndarray:
        """
        Annotate frame with detection information.
        
        Args:
            frame: Input frame
            detections: List of detection dictionaries
            show_confidence: Whether to show confidence scores
            show_labels: Whether to show labels
            
        Returns:
            Annotated frame
        """
        annotated = frame.copy()
        
        for i, detection in enumerate(detections):
            # Get bounding box
            bbox = detection.get('bbox', [])
            if len(bbox) != 4:
                continue
                
            x1, y1, x2, y2 = map(int, bbox)
            
            # Get color for this detection
            color = self.colors[i % len(self.colors)]
            
            # Draw bounding box
            cv2.rectangle(annotated, (x1, y1), (x2, y2), color, self.box_thickness)
            
            # Prepare label text
            label_parts = []
            
            if show_labels:
                label = detection.get('label', 'Detection')
                if 'track_id' in detection:
                    label_parts.append(f"{label} #{detection['track_id']}")
                else:
                    label_parts.append(label)
            
            if show_confidence and 'score' in detection:
                confidence = detection['score']
                label_parts.append(f"{confidence:.{self.confidence_precision}f}")
            
            # Draw label if we have text
            if label_parts:
                label_text = " | ".join(label_parts)
                self._draw_label(annotated, label_text, (x1, y1), color)
        
        return annotated
    
    def _draw_label(self, frame: np.ndarray, text: str, position: Tuple[int, int], color: Tuple[int, int, int]):
        """Draw label with background."""
        x, y = position
        
        # Get text size
        (text_w, text_h), baseline = cv2.getTextSize(
            text, self.font, self.label_font_scale, self.label_thickness
        )
        
        # Calculate label background rectangle
        label_y = max(0, y - text_h - 2 * self.label_padding)
        label_h = text_h + 2 * self.label_padding
        
        # Draw label background
        cv2.rectangle(
            frame,
            (x, label_y),
            (x + text_w + 2 * self.label_padding, label_y + label_h),
            color,
            -1
        )
        
        # Draw text
        text_y = label_y + text_h + self.label_padding
        cv2.putText(
            frame,
            text,
            (x + self.label_padding, text_y),
            self.font,
            self.label_font_scale,
            (255, 255, 255),  # White text
            self.label_thickness
        )


class PerformanceVisualizer:
    """
    Performance visualization utilities for monitoring system performance.
    """
    
    def __init__(self, history_length: int = 100):
        """
        Initialize performance visualizer.
        
        Args:
            history_length: Number of performance samples to keep
        """
        self.history_length = history_length
        self.fps_history = []
        self.detection_history = []
        self.timestamp_history = []
    
    def update(self, fps: float, detection_count: int):
        """Update performance history."""
        current_time = time.time()
        
        self.fps_history.append(fps)
        self.detection_history.append(detection_count)
        self.timestamp_history.append(current_time)
        
        # Trim history to maintain size
        if len(self.fps_history) > self.history_length:
            self.fps_history.pop(0)
            self.detection_history.pop(0)
            self.timestamp_history.pop(0)
    
    def draw_performance_graph(self, frame: np.ndarray, position: str = 'bottom-right') -> np.ndarray:
        """
        Draw performance graph on frame.
        
        Args:
            frame: Input frame
            position: Position for graph
            
        Returns:
            Frame with performance graph
        """
        if len(self.fps_history) < 2:
            return frame
        
        h, w = frame.shape[:2]
        graph_w, graph_h = 200, 100
        margin = 10
        
        # Calculate position
        if position == 'bottom-right':
            graph_x = w - graph_w - margin
            graph_y = h - graph_h - margin
        elif position == 'bottom-left':
            graph_x = margin
            graph_y = h - graph_h - margin
        elif position == 'top-right':
            graph_x = w - graph_w - margin
            graph_y = margin
        else:  # top-left
            graph_x = margin
            graph_y = margin
        
        # Draw graph background
        overlay = frame.copy()
        cv2.rectangle(overlay, (graph_x, graph_y), (graph_x + graph_w, graph_y + graph_h), (0, 0, 0), -1)
        frame = cv2.addWeighted(frame, 0.8, overlay, 0.2, 0)
        
        # Draw FPS graph
        if self.fps_history:
            max_fps = max(self.fps_history) if self.fps_history else 1
            min_fps = min(self.fps_history) if self.fps_history else 0
            fps_range = max_fps - min_fps if max_fps > min_fps else 1
            
            points = []
            for i, fps in enumerate(self.fps_history):
                x = graph_x + int((i / len(self.fps_history)) * graph_w)
                y = graph_y + graph_h - int(((fps - min_fps) / fps_range) * graph_h)
                points.append((x, y))
            
            # Draw FPS line
            for i in range(1, len(points)):
                cv2.line(frame, points[i-1], points[i], (0, 255, 0), 1)
        
        # Add labels
        cv2.putText(frame, "FPS", (graph_x + 5, graph_y + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        
        return frame
    
    def get_performance_summary(self) -> Dict[str, float]:
        """Get performance summary statistics."""
        if not self.fps_history:
            return {}
        
        return {
            'avg_fps': sum(self.fps_history) / len(self.fps_history),
            'min_fps': min(self.fps_history),
            'max_fps': max(self.fps_history),
            'avg_detections': sum(self.detection_history) / len(self.detection_history) if self.detection_history else 0,
            'samples': len(self.fps_history)
        }
