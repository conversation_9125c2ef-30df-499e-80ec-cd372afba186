"""
Ray VPF (Video Processing Framework) - Core Module

This module contains the core abstract base classes and data structures
that define the framework's architecture.
"""

from .abstractions import (
    FrameSource,
    InferenceModel, 
    FrameProcessor,
    FrameSink
)

from .data_structures import (
    Detection,
    FramePacket
)

__all__ = [
    'FrameSource',
    'InferenceModel',
    'FrameProcessor', 
    'FrameSink',
    'Detection',
    'FramePacket'
]
