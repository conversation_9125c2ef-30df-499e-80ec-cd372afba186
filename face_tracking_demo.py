#!/usr/bin/env python3
"""
Comprehensive Face Detection and Tracking Demonstration

This program showcases the enhanced supervision integration capabilities of the
showcase_framework.py file, demonstrating professional face detection and tracking
with real-time visualization and performance monitoring.

Features:
- Real-time face detection using Hailo RetinaFace model
- Object tracking with unique IDs and persistence
- Professional visualization with supervision library
- Performance metrics and statistics
- Support for webcam and video file input
- Configurable tracking sensitivity and confidence thresholds
- Tracking trails and history visualization
- Comprehensive error handling and graceful degradation

Usage:
    python face_tracking_demo.py --video-file video.mp4 --conf 0.5 --tracking-sensitivity medium
    python face_tracking_demo.py --webcam --conf 0.3 --tracking-sensitivity high --show-trails
    python face_tracking_demo.py --help
"""

import sys
import os
import argparse
import logging
import time
import threading
from collections import defaultdict, deque
from typing import Dict, List, Optional, Tuple, Any
import queue

import cv2
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import enhanced showcase framework components
try:
    from showcase_framework import (
        # Core classes
        VideoFileSource, Picamera2Source, DegirumRetinaFaceModel,
        BufferedPipeline, FramePacket,
        
        # Enhanced supervision integration
        EnhancedFrameProcessor, SupervisionConverter, SupervisionTracker,
        DetectionSourceFactory, create_hailo_showcase_processor,
        
        # Utility functions
        convert_detections_to_supervision,
        
        # Check supervision availability
        SUPERVISION_AVAILABLE
    )
    
    if SUPERVISION_AVAILABLE:
        import supervision as sv
        
except ImportError as e:
    print(f"Error importing showcase_framework: {e}")
    print("Please ensure showcase_framework.py is in the current directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)s] %(asctime)s - %(name)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)


class ConsoleDisplay:
    """Console-based display for face tracking statistics."""

    def __init__(self, update_interval: float = 2.0):
        self.update_interval = update_interval
        self.last_update = 0
        self.frame_count = 0

    def update(self, stats: Dict[str, Any], frame_count: int) -> None:
        """Update console display with current statistics."""
        current_time = time.time()
        self.frame_count = frame_count

        if current_time - self.last_update >= self.update_interval:
            self._print_stats(stats)
            self.last_update = current_time

    def _print_stats(self, stats: Dict[str, Any]) -> None:
        """Print formatted statistics to console."""
        # Clear previous lines (simple approach)
        print("\n" + "="*60)
        print("FACE TRACKING STATISTICS")
        print("="*60)
        print(f"Runtime: {stats['runtime']:.1f}s | Frames: {stats['total_frames']}")
        print(f"Current FPS: {stats['current_fps']:.1f} | Average FPS: {stats['avg_fps']:.1f}")
        print(f"Active Faces: {stats['active_tracks']} | Total Unique: {stats['unique_faces_seen']}")
        print(f"Detection Rate: {stats['face_detection_rate']:.1%} | Max Concurrent: {stats['max_concurrent_faces']}")
        if stats['longest_track_duration'] > 0:
            print(f"Longest Track: {stats['longest_track_duration']} frames")
        print("="*60)
        print("Press Ctrl+C to stop...")

    def final_report(self, stats: Dict[str, Any], output_filename: str = None, raw_output_filename: str = None) -> None:
        """Print final statistics report."""
        print("\n" + "="*60)
        print("FINAL FACE TRACKING REPORT")
        print("="*60)
        print(f"Total Runtime: {stats['runtime']:.1f} seconds")
        print(f"Frames Processed: {stats['total_frames']}")
        print(f"Average FPS: {stats['avg_fps']:.1f}")
        print(f"Frames with Faces: {stats['frames_with_faces']} ({stats['face_detection_rate']:.1%})")
        print(f"Total Face Detections: {stats['total_detections']}")
        print(f"Unique Faces Tracked: {stats['unique_faces_seen']}")
        print(f"Maximum Concurrent Faces: {stats['max_concurrent_faces']}")
        print(f"Longest Track Duration: {stats['longest_track_duration']} frames")
        if output_filename:
            print(f"Output Video Saved: {output_filename}")
        if raw_output_filename:
            print(f"Raw Video Saved: {raw_output_filename}")
        print("="*60)


class FaceTrackingStats:
    """Statistics tracker for face detection and tracking performance."""
    
    def __init__(self):
        self._lock = threading.Lock()
        self.reset()

    def reset(self):
        """Reset all statistics."""
        with self._lock:
            self.total_frames = 0
            self.frames_with_faces = 0
            self.total_detections = 0
            self.active_tracks = 0
            self.max_concurrent_faces = 0
            self.track_durations = defaultdict(int)  # track_id -> frame_count
            self.fps_history = deque(maxlen=30)
            self.start_time = time.time()
            self.last_frame_time = time.time()
    
    def update_frame(self, num_faces: int, active_track_ids: List[int]):
        """Update statistics for a new frame."""
        with self._lock:
            current_time = time.time()
            
            # Frame statistics
            self.total_frames += 1
            if num_faces > 0:
                self.frames_with_faces += 1
                self.total_detections += num_faces
            
            # Tracking statistics
            self.active_tracks = len(active_track_ids)
            self.max_concurrent_faces = max(self.max_concurrent_faces, num_faces)
            
            # Update track durations
            for track_id in active_track_ids:
                self.track_durations[track_id] += 1
            
            # FPS calculation
            if self.last_frame_time > 0:
                frame_time = current_time - self.last_frame_time
                if frame_time > 0:
                    fps = 1.0 / frame_time
                    self.fps_history.append(fps)
            
            self.last_frame_time = current_time
    
    def get_current_fps(self) -> float:
        """Get current FPS based on recent frames."""
        with self._lock:
            if len(self.fps_history) > 0:
                return sum(self.fps_history) / len(self.fps_history)
            return 0.0
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive statistics summary."""
        with self._lock:
            runtime = time.time() - self.start_time
            avg_fps = self.total_frames / max(runtime, 0.001)

            # Calculate current FPS without recursive lock
            current_fps = 0.0
            if len(self.fps_history) > 0:
                current_fps = sum(self.fps_history) / len(self.fps_history)

            return {
                'runtime': runtime,
                'total_frames': self.total_frames,
                'frames_with_faces': self.frames_with_faces,
                'total_detections': self.total_detections,
                'active_tracks': self.active_tracks,
                'max_concurrent_faces': self.max_concurrent_faces,
                'unique_faces_seen': len(self.track_durations),
                'avg_fps': avg_fps,
                'current_fps': current_fps,
                'face_detection_rate': self.frames_with_faces / max(self.total_frames, 1),
                'avg_detections_per_frame': self.total_detections / max(self.total_frames, 1),
                'longest_track_duration': max(self.track_durations.values()) if self.track_durations else 0
            }


class TrackingTrailsVisualizer:
    """Visualizer for tracking trails and history."""
    
    def __init__(self, max_trail_length: int = 30, trail_thickness: int = 2):
        self.max_trail_length = max_trail_length
        self.trail_thickness = trail_thickness
        self.trails = defaultdict(lambda: deque(maxlen=max_trail_length))
        self.colors = {}
        self._color_generator = self._generate_colors()
    
    def _generate_colors(self):
        """Generate distinct colors for different tracks."""
        colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
            (255, 0, 255), (0, 255, 255), (255, 128, 0), (128, 255, 0),
            (255, 0, 128), (128, 0, 255), (0, 128, 255), (0, 255, 128)
        ]
        while True:
            for color in colors:
                yield color
    
    def update_trails(self, detections: "sv.Detections"):
        """Update tracking trails with new detections."""
        if not hasattr(detections, 'tracker_id') or detections.tracker_id is None:
            return
        
        for i, track_id in enumerate(detections.tracker_id):
            if track_id is not None and i < len(detections.xyxy):
                # Get center point of bounding box
                x1, y1, x2, y2 = detections.xyxy[i]
                center = (int((x1 + x2) / 2), int((y1 + y2) / 2))
                
                # Add to trail
                self.trails[track_id].append(center)
                
                # Assign color if new track
                if track_id not in self.colors:
                    self.colors[track_id] = next(self._color_generator)
    
    def draw_trails(self, frame: np.ndarray) -> np.ndarray:
        """Draw tracking trails on the frame."""
        overlay = frame.copy()
        
        for track_id, trail in self.trails.items():
            if len(trail) < 2:
                continue
            
            color = self.colors.get(track_id, (255, 255, 255))
            points = list(trail)
            
            # Draw trail with fading effect
            for i in range(1, len(points)):
                alpha = i / len(points)  # Fade from 0 to 1
                thickness = max(1, int(self.trail_thickness * alpha))
                
                cv2.line(overlay, points[i-1], points[i], color, thickness)
        
        # Blend with original frame
        return cv2.addWeighted(frame, 0.7, overlay, 0.3, 0)
    
    def cleanup_old_trails(self, active_track_ids: List[int]):
        """Remove trails for inactive tracks."""
        inactive_tracks = set(self.trails.keys()) - set(active_track_ids)
        for track_id in inactive_tracks:
            if track_id in self.trails:
                del self.trails[track_id]
            if track_id in self.colors:
                del self.colors[track_id]


class AdvancedFaceTrackingProcessor(EnhancedFrameProcessor):
    """
    Advanced face tracking processor with enhanced visualization and statistics.
    
    This processor extends the EnhancedFrameProcessor to provide:
    - Advanced tracking trails visualization
    - Real-time performance statistics
    - Professional annotations with tracking information
    - Configurable display options
    """
    
    def __init__(
        self,
        conf_threshold: float = 0.5,
        tracking_sensitivity: str = 'medium',
        show_trails: bool = False,
        show_stats: bool = True,
        trail_length: int = 30,
        enable_tracking: bool = True,
        **kwargs
    ):
        # Configure tracking parameters based on sensitivity
        tracking_params = self._get_tracking_params(tracking_sensitivity)

        super().__init__(
            conf_threshold=conf_threshold,
            enable_supervision=True,
            enable_tracking=enable_tracking,
            tracker_config=tracking_params,
            **kwargs
        )
        
        # Visualization components
        self.show_trails = show_trails
        self.show_stats = show_stats
        self.trails_visualizer = TrackingTrailsVisualizer(max_trail_length=trail_length)
        self.stats = FaceTrackingStats()
        
        logger.info(f"AdvancedFaceTrackingProcessor initialized with tracking_sensitivity='{tracking_sensitivity}', "
                   f"show_trails={show_trails}, show_stats={show_stats}")
    
    def _get_tracking_params(self, sensitivity: str) -> Dict[str, Any]:
        """Get tracking parameters based on sensitivity level."""
        sensitivity_configs = {
            'low': {
                'track_activation_threshold': 0.7,
                'lost_track_buffer': 15,
                'minimum_matching_threshold': 0.9,
                'enable_smoothing': False
            },
            'medium': {
                'track_activation_threshold': 0.5,
                'lost_track_buffer': 30,
                'minimum_matching_threshold': 0.8,
                'enable_smoothing': True
            },
            'high': {
                'track_activation_threshold': 0.3,
                'lost_track_buffer': 50,
                'minimum_matching_threshold': 0.7,
                'enable_smoothing': True
            }
        }
        
        return sensitivity_configs.get(sensitivity, sensitivity_configs['medium'])

    def process(self, pkt: FramePacket) -> np.ndarray:
        """
        Process frame with advanced face tracking and visualization.

        Args:
            pkt: FramePacket containing frame and model output

        Returns:
            Processed frame with professional annotations and tracking
        """
        try:
            # Debug: Log model output type and structure
            logger.debug(f"Processing frame {pkt.frame_idx}, model_out type: {type(pkt.model_out)}")

            # First try to convert without tracking to isolate the issue
            if not self.enable_supervision:
                logger.warning("Supervision not enabled, returning original frame")
                return pkt.frame_rgb.copy()

            # Convert model output to supervision format
            h, w = pkt.frame_rgb.shape[:2]
            sv_detections = self.converter.convert_to_supervision(
                pkt.model_out,
                source_format='hailo',
                frame_shape=(h, w),
                class_names=None
            )

            logger.debug(f"Converted to supervision: {sv_detections is not None}, count: {len(sv_detections) if sv_detections else 0}")

            # Apply tracking if enabled and we have detections
            if self.tracker and self.tracker.is_available() and sv_detections is not None and len(sv_detections) > 0:
                logger.debug("Applying tracking...")
                try:
                    sv_detections = self.tracker.update_with_detections(sv_detections)
                    logger.debug(f"Tracking applied, count: {len(sv_detections) if sv_detections else 0}")
                except Exception as e:
                    logger.error(f"Tracking failed: {e}")
                    # Continue without tracking

            if sv_detections is None or len(sv_detections) == 0:
                # Update stats for frame with no faces
                self.stats.update_frame(0, [])
                return self._draw_stats_overlay(pkt.frame_rgb.copy(), sv_detections)

            # Extract tracking information
            active_track_ids = []
            if hasattr(sv_detections, 'tracker_id') and sv_detections.tracker_id is not None:
                active_track_ids = [tid for tid in sv_detections.tracker_id if tid is not None]

            # Update statistics
            self.stats.update_frame(len(sv_detections), active_track_ids)

            # Start with original frame
            annotated_frame = pkt.frame_rgb.copy()

            # Draw tracking trails if enabled
            if self.show_trails and active_track_ids:
                self.trails_visualizer.update_trails(sv_detections)
                annotated_frame = self.trails_visualizer.draw_trails(annotated_frame)
                self.trails_visualizer.cleanup_old_trails(active_track_ids)

            # Apply professional supervision annotations
            if self.box_annotator:
                annotated_frame = self.box_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections
                )

            if self.label_annotator:
                labels = self._create_enhanced_labels(sv_detections)
                annotated_frame = self.label_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections,
                    labels=labels
                )

            # Add statistics overlay if enabled
            if self.show_stats:
                annotated_frame = self._draw_stats_overlay(annotated_frame, sv_detections)

            return annotated_frame

        except Exception as e:
            logger.error(f"Error in face tracking processing: {e}")
            logger.debug(f"Model output type: {type(pkt.model_out)}, frame shape: {pkt.frame_rgb.shape}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            # Fallback to basic processing
            try:
                return super().process(pkt)
            except Exception as e2:
                logger.error(f"Fallback processing also failed: {e2}")
                # Return original frame as last resort
                return pkt.frame_rgb.copy()

    def _create_enhanced_labels(self, sv_detections: "sv.Detections") -> List[str]:
        """Create enhanced labels with tracking IDs and confidence scores."""
        labels = []

        for i in range(len(sv_detections)):
            label_parts = []

            # Add tracking ID if available
            if hasattr(sv_detections, 'tracker_id') and sv_detections.tracker_id is not None:
                if i < len(sv_detections.tracker_id) and sv_detections.tracker_id[i] is not None:
                    label_parts.append(f"Face #{sv_detections.tracker_id[i]}")
                else:
                    label_parts.append("Face")
            else:
                label_parts.append("Face")

            # Add confidence score
            if i < len(sv_detections.confidence):
                confidence = sv_detections.confidence[i]
                label_parts.append(f"{confidence:.2f}")

            labels.append(" | ".join(label_parts))

        return labels

    def _draw_stats_overlay(self, frame: np.ndarray, sv_detections: Optional["sv.Detections"]) -> np.ndarray:
        """Draw performance statistics overlay on the frame."""
        if not self.show_stats:
            return frame

        stats = self.stats.get_summary()

        # Prepare statistics text
        stats_lines = [
            f"FPS: {stats['current_fps']:.1f} (Avg: {stats['avg_fps']:.1f})",
            f"Faces: {len(sv_detections) if sv_detections else 0} | Active Tracks: {stats['active_tracks']}",
            f"Total Frames: {stats['total_frames']} | With Faces: {stats['frames_with_faces']}",
            f"Unique Faces: {stats['unique_faces_seen']} | Max Concurrent: {stats['max_concurrent_faces']}",
            f"Detection Rate: {stats['face_detection_rate']:.1%}",
            f"Runtime: {stats['runtime']:.1f}s"
        ]

        # Draw semi-transparent background
        overlay = frame.copy()
        stats_height = len(stats_lines) * 25 + 20
        cv2.rectangle(overlay, (10, 10), (400, stats_height), (0, 0, 0), -1)
        frame = cv2.addWeighted(frame, 0.7, overlay, 0.3, 0)

        # Draw statistics text
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        color = (255, 255, 255)
        thickness = 1

        for i, line in enumerate(stats_lines):
            y = 30 + i * 25
            cv2.putText(frame, line, (15, y), font, font_scale, color, thickness)

        return frame

    def get_statistics(self) -> Dict[str, Any]:
        """Get current tracking statistics."""
        return self.stats.get_summary()

    def reset_statistics(self):
        """Reset tracking statistics."""
        self.stats.reset()


def create_video_source(args) -> Tuple[Any, str]:
    """Create appropriate video source based on arguments."""
    if args.video_file:
        if not os.path.exists(args.video_file):
            raise FileNotFoundError(f"Video file not found: {args.video_file}")

        source = VideoFileSource(
            path=args.video_file,
            realtime=args.realtime,
            loop=args.loop
        )
        source_info = f"Video file: {args.video_file}"

    elif args.webcam:
        # On Raspberry Pi, --webcam should prefer Picamera2 over USB webcam
        try:
            # Try Picamera2 first (built-in Raspberry Pi camera)
            # source = Picamera2Source(size=(1280, 720))
            # source = Picamera2Source(size=(640, 480))
            source = Picamera2Source(size=(800, 600))
            source_info = "Picamera2 (Raspberry Pi camera)"
            logger.info("Using Picamera2 for --webcam option")

        except Exception as e:
            logger.warning(f"Picamera2 not available: {e}")
            try:
                # Fallback to USB webcam (camera index 0)
                import cv2
                cap = cv2.VideoCapture(0)
                if not cap.isOpened():
                    raise RuntimeError("Cannot open USB webcam")
                cap.release()

                # Create a simple webcam source
                class WebcamSource:
                    def __init__(self, camera_index=0):
                        self.camera_index = camera_index
                        self._cap = None
                        self._stopped = True

                    def start(self):
                        self._cap = cv2.VideoCapture(self.camera_index)
                        if not self._cap.isOpened():
                            raise RuntimeError(f"Cannot open camera {self.camera_index}")
                        self._stopped = False
                        logger.info(f"USB webcam source started: camera {self.camera_index}")

                    def read(self):
                        if self._cap is None or self._stopped:
                            return None
                        ret, frame_bgr = self._cap.read()
                        if not ret:
                            return None
                        return cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)

                    def stop(self):
                        self._stopped = True
                        if self._cap is not None:
                            self._cap.release()
                            self._cap = None
                        logger.info("USB webcam source stopped")

                source = WebcamSource(0)
                source_info = "USB Webcam (camera 0)"
                logger.info("Using USB webcam as fallback for --webcam option")

            except Exception as e2:
                raise RuntimeError(f"No camera source available. Picamera2 error: {e}, USB webcam error: {e2}")

    else:
        # Default to Picamera2
        try:
            source = Picamera2Source(size=(1280, 720))
            source_info = "Picamera2 (Raspberry Pi camera)"
        except Exception as e:
            raise RuntimeError(f"Picamera2 not available: {e}. Please specify --video-file or --webcam")

    return source, source_info


def create_face_detection_model(args) -> Tuple[Any, str]:
    """Create face detection model."""
    try:
        model = DegirumRetinaFaceModel(
            model_name="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
            zoo_path=args.zoo_path
        )
        model_info = "Hailo RetinaFace (retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1)"

    except Exception as e:
        logger.warning(f"Failed to load RetinaFace model: {e}")

        # Try alternative face detection models
        alternative_models = [
            "yolov8n_relu6_person--640x640_quant_hailort_multidevice_1",
            "hand_landmark_lite--224x224_quant_hailort_hailo8l_1"
        ]

        model = None
        model_info = "No model available"

        for alt_model in alternative_models:
            try:
                model = DegirumRetinaFaceModel(
                    model_name=alt_model,
                    zoo_path=args.zoo_path
                )
                model_info = f"Alternative model: {alt_model}"
                logger.info(f"Using alternative model: {alt_model}")
                break
            except Exception as e2:
                logger.debug(f"Alternative model {alt_model} failed: {e2}")
                continue

        if model is None:
            # Create dummy model for testing
            from showcase_framework import DummyModel
            model = DummyModel()
            model_info = "Dummy model (for testing only)"
            logger.warning("Using dummy model - no real face detection will occur")

    return model, model_info


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Comprehensive Face Detection and Tracking Demonstration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Use video file with medium tracking sensitivity (GUI mode)
    python face_tracking_demo.py --video-file video.mp4 --conf 0.5 --tracking-sensitivity medium

    # Use webcam with high sensitivity and tracking trails (GUI mode)
    python face_tracking_demo.py --webcam --conf 0.3 --tracking-sensitivity high --show-trails

    # Console mode with text-based statistics
    python face_tracking_demo.py --video-file video.mp4 --console --log-performance

    # Headless mode with performance logging (no display)
    python face_tracking_demo.py --video-file video.mp4 --headless --log-performance

    # Save output video with custom settings
    python face_tracking_demo.py --video-file input.mp4 --output output.mp4 --conf 0.4 --show-trails

    # Save both processed and raw footage
    python face_tracking_demo.py --video-file input.mp4 --output processed.mp4 --raw-output raw.mp4
        """
    )

    # Input source options
    input_group = parser.add_mutually_exclusive_group()
    input_group.add_argument(
        "--video-file", type=str,
        help="Path to input video file"
    )
    input_group.add_argument(
        "--webcam", action="store_true",
        help="Use camera as input source (Picamera2 on Raspberry Pi, USB webcam as fallback)"
    )

    # Model and detection options
    parser.add_argument(
        "--zoo-path", type=str, default="~/degirum-zoo",
        help="Path to DeGirum model zoo (default: ~/degirum-zoo)"
    )
    parser.add_argument(
        "--conf", "--confidence", type=float, default=0.5,
        help="Confidence threshold for face detection (default: 0.5)"
    )

    # Tracking options
    parser.add_argument(
        "--tracking-sensitivity", type=str, default="medium",
        choices=["low", "medium", "high"],
        help="Tracking sensitivity level (default: medium)"
    )
    parser.add_argument(
        "--no-tracking", action="store_true",
        help="Disable object tracking (detection only)"
    )

    # Visualization options
    parser.add_argument(
        "--show-trails", action="store_true",
        help="Show tracking trails/history"
    )
    parser.add_argument(
        "--trail-length", type=int, default=30,
        help="Maximum length of tracking trails (default: 30)"
    )
    parser.add_argument(
        "--no-stats", action="store_true",
        help="Hide performance statistics overlay"
    )

    # Display mode options
    display_group = parser.add_mutually_exclusive_group()
    display_group.add_argument(
        "--headless", action="store_true",
        help="Run without display window (headless mode)"
    )
    display_group.add_argument(
        "--console", action="store_true",
        help="Run in console mode with text-based statistics"
    )

    # Output options
    parser.add_argument(
        "--output", type=str,
        help="Path to save output video file"
    )
    parser.add_argument(
        "--raw-output", type=str,
        help="Path to save raw (unprocessed) video file"
    )
    parser.add_argument(
        "--fps", type=float, default=30.0,
        help="Output video FPS (default: 30.0)"
    )

    # Video source options
    parser.add_argument(
        "--realtime", action="store_true",
        help="Play video in real-time (for video files)"
    )
    parser.add_argument(
        "--loop", action="store_true",
        help="Loop video playback (for video files)"
    )

    # Performance and debugging
    parser.add_argument(
        "--log-performance", action="store_true",
        help="Log detailed performance statistics"
    )
    parser.add_argument(
        "--debug", action="store_true",
        help="Enable debug logging"
    )
    parser.add_argument(
        "--max-frames", type=int,
        help="Maximum number of frames to process (for testing)"
    )

    return parser.parse_args()


def setup_logging(debug: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if debug else logging.INFO

    # Configure root logger
    logging.getLogger().setLevel(level)

    # Configure specific loggers
    logging.getLogger('showcase_framework').setLevel(level)
    logging.getLogger(__name__).setLevel(level)

    if debug:
        logger.info("Debug logging enabled")


def print_system_info(args, source_info: str, model_info: str):
    """Print system configuration information."""
    print("\n" + "="*60)
    print("FACE TRACKING DEMO - SYSTEM CONFIGURATION")
    print("="*60)
    print(f"Video Source: {source_info}")
    print(f"Detection Model: {model_info}")
    print(f"Confidence Threshold: {args.conf}")
    print(f"Tracking: {'Disabled' if args.no_tracking else f'Enabled ({args.tracking_sensitivity} sensitivity)'}")
    print(f"Supervision Available: {SUPERVISION_AVAILABLE}")
    print(f"Visualization: Trails={'Yes' if args.show_trails else 'No'}, Stats={'No' if args.no_stats else 'Yes'}")
    display_mode = 'Headless' if args.headless else ('Console' if args.console else 'GUI')
    print(f"Display Mode: {display_mode}")
    if args.output:
        print(f"Output Video: {args.output} @ {args.fps} FPS")
    if args.raw_output:
        print(f"Raw Video: {args.raw_output} @ {args.fps} FPS")
    print("="*60)
    if args.console or args.headless:
        print("Video will exit automatically when processing completes")
    else:
        print("Press 'q' to quit, 'r' to reset statistics, 's' to save screenshot")
    print("="*60 + "\n")


def main():
    """Main function for face tracking demonstration."""
    # Parse arguments
    args = parse_arguments()

    # Setup logging
    setup_logging(args.debug)

    # Check supervision availability
    if not SUPERVISION_AVAILABLE:
        logger.error("Supervision library not available!")
        print("Please install supervision: pip install supervision")
        return 1

    try:
        # Create video source
        logger.info("Creating video source...")
        source, source_info = create_video_source(args)

        # Create face detection model
        logger.info("Loading face detection model...")
        model, model_info = create_face_detection_model(args)

        # Create advanced face tracking processor
        logger.info("Creating face tracking processor...")
        processor = AdvancedFaceTrackingProcessor(
            conf_threshold=args.conf,
            tracking_sensitivity=args.tracking_sensitivity,
            show_trails=args.show_trails,
            show_stats=not args.no_stats,
            trail_length=args.trail_length,
            enable_tracking=not args.no_tracking
        )

        # Print system information
        print_system_info(args, source_info, model_info)

        # Create output sink if specified or default for webcam
        sink = None
        raw_sink = None
        output_filename = args.output
        raw_output_filename = args.raw_output

        # If no output specified but using webcam, create a default output filename
        if not output_filename and args.webcam:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"face_tracking_webcam_{timestamp}.mp4"
            logger.info(f"No output specified for webcam mode, using default: {output_filename}")

        if output_filename:
            from showcase_framework import OpenCVFileSink
            sink = OpenCVFileSink(output_filename, fps=args.fps)
            logger.info(f"Output will be saved to: {output_filename}")

        # Create raw output sink if specified
        if raw_output_filename:
            from showcase_framework import OpenCVFileSink
            raw_sink = OpenCVFileSink(raw_output_filename, fps=args.fps)
            logger.info(f"Raw footage will be saved to: {raw_output_filename}")

        # Create pipeline
        logger.info("Creating processing pipeline...")
        pipeline = BufferedPipeline(
            source=source,
            model=model,
            processor=processor,
            sink=sink,
            raw_sink=raw_sink,
            input_q_size=8,
            proc_q_size=8,
            preview_q_size=2
        )

        # Start pipeline
        logger.info("Starting face tracking pipeline...")
        pipeline.start()

        # Setup display mode
        console_display = None
        window_name = None

        if args.console:
            # Console mode with text-based statistics
            console_display = ConsoleDisplay(update_interval=2.0)
            logger.info("Console mode enabled - statistics will be displayed in terminal")
        elif not args.headless:
            # GUI mode with OpenCV window
            try:
                window_name = "Face Tracking Demo - Press 'q' to quit"
                cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                cv2.resizeWindow(window_name, 1280, 720)
                logger.info("GUI mode enabled - video window opened")
            except Exception as e:
                logger.warning(f"Failed to create GUI window: {e}")
                logger.info("Falling back to console mode")
                console_display = ConsoleDisplay(update_interval=2.0)
                window_name = None
        else:
            logger.info("Headless mode enabled - no display output")

        frame_count = 0
        last_stats_time = time.time()

        try:
            while True:
                # Check for maximum frames limit
                if args.max_frames and frame_count >= args.max_frames:
                    logger.info(f"Reached maximum frames limit: {args.max_frames}")
                    break

                # Get processed frame from pipeline
                try:
                    processed_frame = pipeline.preview_q.get(timeout=1.0)
                    frame_count += 1
                except queue.Empty:
                    # Check if pipeline has signaled completion (video ended)
                    if hasattr(pipeline, '_stop_ev') and pipeline._stop_ev.is_set():
                        logger.info("Pipeline finished - video source ended")
                        break
                    continue

                if processed_frame is None:
                    logger.info("End of video stream")
                    break

                # Handle different display modes
                if window_name is not None:
                    # GUI mode with OpenCV window
                    try:
                        # Convert RGB to BGR for OpenCV display
                        display_frame = cv2.cvtColor(processed_frame, cv2.COLOR_RGB2BGR)
                        cv2.imshow(window_name, display_frame)

                        # Handle keyboard input
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q'):
                            logger.info("Quit requested by user")
                            break
                        elif key == ord('r'):
                            processor.reset_statistics()
                            logger.info("Statistics reset")
                        elif key == ord('s'):
                            # Save screenshot
                            timestamp = time.strftime("%Y%m%d_%H%M%S")
                            screenshot_path = f"face_tracking_screenshot_{timestamp}.jpg"
                            cv2.imwrite(screenshot_path, display_frame)
                            logger.info(f"Screenshot saved: {screenshot_path}")
                    except Exception as e:
                        logger.warning(f"Display error: {e}")
                        # Fall back to console mode
                        if console_display is None:
                            console_display = ConsoleDisplay(update_interval=2.0)
                        window_name = None

                elif console_display is not None:
                    # Console mode with text-based statistics
                    stats = processor.get_statistics()
                    console_display.update(stats, frame_count)

                # Log performance statistics periodically
                if args.log_performance:
                    current_time = time.time()
                    if current_time - last_stats_time >= 10.0:  # Every 10 seconds
                        stats = processor.get_statistics()
                        logger.info(f"Performance: FPS={stats['current_fps']:.1f}, "
                                  f"Faces={stats['active_tracks']}, "
                                  f"Total={stats['unique_faces_seen']}")
                        last_stats_time = current_time

        except KeyboardInterrupt:
            logger.info("Interrupted by user")

        finally:
            # Cleanup
            logger.info("Stopping pipeline...")
            try:
                pipeline.stop_all()
                # Give threads time to finish cleanly
                pipeline.join(timeout=5.0)
            except Exception as e:
                logger.warning(f"Pipeline cleanup error: {e}")

            # Close GUI window if it was opened
            if window_name is not None:
                try:
                    cv2.destroyAllWindows()
                    # Give OpenCV time to cleanup
                    cv2.waitKey(1)
                except Exception:
                    pass

            # Print final statistics
            final_stats = processor.get_statistics()

            if console_display is not None:
                # Use console display for final report
                console_display.final_report(final_stats, output_filename, raw_output_filename)
            else:
                # Standard final statistics output
                print("\n" + "="*60)
                print("FINAL STATISTICS")
                print("="*60)
                print(f"Total Runtime: {final_stats['runtime']:.1f} seconds")
                print(f"Frames Processed: {final_stats['total_frames']}")
                print(f"Average FPS: {final_stats['avg_fps']:.1f}")
                print(f"Frames with Faces: {final_stats['frames_with_faces']} ({final_stats['face_detection_rate']:.1%})")
                print(f"Total Detections: {final_stats['total_detections']}")
                print(f"Unique Faces Tracked: {final_stats['unique_faces_seen']}")
                print(f"Max Concurrent Faces: {final_stats['max_concurrent_faces']}")
                print(f"Longest Track Duration: {final_stats['longest_track_duration']} frames")
                if output_filename:
                    print(f"Output Video Saved: {output_filename}")
                if raw_output_filename:
                    print(f"Raw Video Saved: {raw_output_filename}")
                print("="*60)

        return 0

    except Exception as e:
        logger.error(f"Error in face tracking demo: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

# cd /home/<USER>/jk/dev/hailotest && source cam-env/bin/activate && python face_tracking_demo.py --video-file videos/people1_30s.mp4 --output full_test_output.mp4 --headless --show-trails --realtime
# python face_tracking_demo.py --output processed.mp4 --console --show-trails --video-file  jk_raw.mp4 --realtime
# python face_tracking_demo.py --output processed.mp4 --console --show-trails --video-file  /home/<USER>/jk/dev/hailotest/jk_raw.avi --realtime