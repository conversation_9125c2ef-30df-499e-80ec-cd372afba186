"""
File sink implementation for Ray VPF.
"""

import time
import queue
import threading
import logging
from typing import Optional

import cv2
import numpy as np

from ..core import FrameSink

logger = logging.getLogger(__name__)


class OpenCVFileSink(FrameSink):
    """Robust file sink for Raspberry Pi using GStreamer for MP4 (H.264) encoding.

    This class fixes a common problem where the output file does not respect the
    requested FPS by:
      - providing explicit caps (including framerate) to the GStreamer pipeline
      - adding `videorate` and `h264parse` where needed
      - and applying a time-based throttle to writes as a fallback

    Note: GStreamer must be available and built with the necessary plugins for
    H.264 encoding for the GStreamer path to work. If the GStreamer writer fails
    we fall back to a simple XVID AVI writer which respects the FPS argument.
    """

    def __init__(self, path: str, fps: float = 15.0):
        self.path = path
        self.fps = float(fps)
        self.width = None
        self.height = None
        self._q: queue.Queue = queue.Queue(maxsize=512)
        self._stop_ev = threading.Event()
        self._thread: Optional[threading.Thread] = None
        self._writer: Optional[cv2.VideoWriter] = None
        self._last_write_ts: Optional[float] = None
        # precompute frame interval
        self._frame_interval = 1.0 / max(1e-6, float(self.fps))

        # write counters & timing for monitoring
        self._written_count = 0
        self._written_lock = threading.Lock()
        self._writer_start_ts: Optional[float] = None

    def _fps_to_gst_fraction(self) -> str:
        """Convert float fps to a rational string for GStreamer caps, e.g. "30/1" or "30000/1001"."""
        try:
            from fractions import Fraction
            f = Fraction(self.fps).limit_denominator(1001)
            return f"{f.numerator}/{f.denominator}"
        except Exception:
            return f"{int(round(self.fps))}/1"

    def start(self):
        """Start the file sink."""
        self._stop_ev.clear()
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()
        logger.info("OpenCVFileSink started: %s", self.path)

    def put(self, frame_rgb: np.ndarray):
        """Write a frame to the sink."""
        # Lazy init writer on first frame
        if self._writer is None:
            h, w = frame_rgb.shape[:2]
            self.width, self.height = w, h
            # Build GStreamer pipeline with explicit caps for framerate and resolution
            gst_fps = self._fps_to_gst_fraction()
            gst_str = (
                f"appsrc is-live=true format=time do-timestamp=true ! "
                f"video/x-raw,format=BGR,framerate={gst_fps},width={w},height={h} ! "
                f"videoconvert ! videorate ! x264enc tune=zerolatency bitrate=500 speed-preset=superfast ! "
                f"h264parse ! mp4mux ! filesink location={self.path}"
            )
            try:
                self._writer = cv2.VideoWriter(
                    gst_str,
                    cv2.CAP_GSTREAMER,
                    0,
                    float(self.fps),
                    (w, h),
                )
            except Exception:
                self._writer = None

            if self._writer is None or not self._writer.isOpened():
                # fallback to XVID AVI
                logger.warning("GStreamer H.264 failed or unavailable, falling back to XVID AVI")
                self.path = self.path.rsplit(".", 1)[0] + ".avi"
                fourcc = cv2.VideoWriter_fourcc(*"XVID")
                self._writer = cv2.VideoWriter(self.path, fourcc, float(self.fps), (w, h))

            if not self._writer.isOpened():
                raise RuntimeError(f"Failed to create video writer for {self.path}")

            # reset last write timestamp so the time throttle starts fresh
            self._last_write_ts = None
            try:
                # writer accepted: record start time for effective FPS computation
                self._writer_start_ts = time.time()
                with self._written_lock:
                    self._written_count = 0
            except Exception:
                pass

        try:
            self._q.put_nowait(frame_rgb)
        except queue.Full:
            _ = self._q.get_nowait()
            self._q.put_nowait(frame_rgb)

    def _run(self):
        """Background thread for writing frames."""
        while not self._stop_ev.is_set() or not self._q.empty():
            try:
                frame = self._q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                # resize if needed
                if frame.shape[:2] != (self.height, self.width):
                    frame = cv2.resize(frame, (self.width, self.height))
                bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

                # Time-based write throttle to make sure we don't write frames faster
                # than the requested FPS. This helps when the input/source is producing
                # frames faster than the desired output FPS or when the writer does
                # not enforce timing.
                now = time.time()
                if self._last_write_ts is not None:
                    elapsed = now - self._last_write_ts
                    if elapsed < self._frame_interval:
                        # Sleep only for the remaining time slice
                        to_wait = self._frame_interval - elapsed
                        # If to_wait is large, cap it to avoid blocking for long durations
                        if to_wait > 0:
                            time.sleep(to_wait)

                # Write the frame
                self._writer.write(bgr)
                self._last_write_ts = time.time()
                # update write counters for monitoring
                try:
                    with self._written_lock:
                        self._written_count += 1
                        wc = int(self._written_count)
                    if wc % 30 == 0:
                        if self._writer_start_ts:
                            elapsed = time.time() - self._writer_start_ts
                            if elapsed > 0.001:
                                fps_eff = float(wc) / float(elapsed)
                                logger.info("Sink wrote %d frames (effective fps: %.2f) to %s", wc, fps_eff, self.path)
                except Exception:
                    pass

            except Exception as e:
                logger.error("Sink write error: %s", e)
            finally:
                try:
                    self._q.task_done()
                except Exception:
                    pass
        if self._writer:
            try:
                self._writer.release()
            except Exception:
                pass
        logger.info("OpenCVFileSink finished writing")

    def stop(self):
        """Stop the file sink."""
        self._stop_ev.set()
        if self._thread is not None:
            self._thread.join(timeout=10.0)
        # Ensure writer is properly closed
        if self._writer is not None:
            try:
                self._writer.release()
                self._writer = None
            except Exception as e:
                logger.debug(f"Writer release error: {e}")
        logger.info("OpenCVFileSink stopped")
