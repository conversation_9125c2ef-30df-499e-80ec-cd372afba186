"""
Base frame processor implementation for Ray VPF.
"""

import logging
import colorsys
from typing import List, It<PERSON>ble, Tuple, Any, Optional

import cv2
import numpy as np

from ..core import FrameProcessor, FramePacket, Detection

logger = logging.getLogger(__name__)


class BaseFrameProcessor(FrameProcessor):
    """
    Base frame processor with common functionality.

    Provides helper methods for parsing detections and drawing boxes.
    """

    def __init__(self, conf_threshold: float = 0.0):
        super().__init__(conf_threshold)

    def process(self, pkt: FramePacket) -> np.ndarray:
        """Default implementation that draws all detections."""
        # Parse detections using the robust helper
        h, w = pkt.frame_rgb.shape[:2]
        detections = self.parse_detections(pkt.model_out, (h, w))
        # Draw boxes and return the frame
        return self.draw_boxes(pkt.frame_rgb, detections)

    def parse_detections(self, model_out: Any, frame_shape: Tuple[int, int]) -> List[Detection]:
        """Normalize arbitrary model outputs into a list of Detection.

        Accepts common shapes:
          - list/tuple of dicts or arrays
          - dict with keys 'results', 'detections', 'objects', 'boxes'
          - numpy arrays (N,4) or (N,5)
          - single dict with 'bbox'/'box' and optional 'score'/'confidence' and 'label'/'class'
        """
        h, w = frame_shape
        raw_items = self._explode_model_output(model_out)
        dets: List[Detection] = []
        for item in raw_items:
            try:
                bbox_raw, score, label = self._extract_bbox_score_label(item)
                if bbox_raw is None:
                    continue
                x1, y1, x2, y2 = self._to_xyxy_pixels(bbox_raw, w, h)
                det = Detection(bbox=(x1, y1, x2, y2), score=score, label=label, raw=item)
                if det.score is None or det.score >= self.conf_threshold:
                    dets.append(det)
            except Exception as e:
                logger.debug("Failed parsing item %s: %s", getattr(item, '__repr__', lambda: item)(), e)
                continue
        return dets

    def draw_boxes(
        self,
        frame_rgb: np.ndarray,
        detections: Iterable[Detection],
        label_field: bool = True,
        score_field: bool = True,
        thickness: int = 2,
    ) -> np.ndarray:
        """Draw boxes (in-place on a copy) and return RGB frame.

        Colors are generated deterministically per label to be stable across frames.
        """
        out = frame_rgb.copy()
        if out.dtype != np.uint8:
            out = (np.clip(out, 0, 255)).astype(np.uint8)

        # OpenCV expects BGR for drawing
        bgr = cv2.cvtColor(out, cv2.COLOR_RGB2BGR)
        h, w = out.shape[:2]

        for det in detections:
            x1, y1, x2, y2 = int(det.bbox[0]), int(det.bbox[1]), int(det.bbox[2]), int(det.bbox[3])
            # clamp
            x1, x2 = max(0, min(w - 1, x1)), max(0, min(w - 1, x2))
            y1, y2 = max(0, min(h - 1, y1)), max(0, min(h - 1, y2))
            label = det.label or ""
            color = self._label_color(label)
            try:
                cv2.rectangle(bgr, (x1, y1), (x2, y2), color, thickness)
                caption_parts = []
                if label_field and label:
                    caption_parts.append(str(label))
                if score_field and det.score is not None:
                    try:
                        caption_parts.append(f"{float(det.score):.2f}")
                    except Exception:
                        caption_parts.append(str(det.score))
                if caption_parts:
                    caption = " ".join(caption_parts)
                    cv2.putText(bgr, caption, (x1, max(0, y1 - 6)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            except Exception as e:
                logger.debug("Failed to draw box: %s", e)
                continue

        return cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)

    def _explode_model_output(self, model_out: Any) -> List[Any]:
        """Return a flat list of items to interpret as detections."""
        if model_out is None:
            return []

        # If an object exposes .results or .detections use that
        for key in ("results", "detections", "objects", "boxes"):
            if hasattr(model_out, key):
                try:
                    container = getattr(model_out, key)
                    return list(container) if container is not None else []
                except Exception:
                    break

        # If dict with one of those keys
        if isinstance(model_out, dict):
            for key in ("results", "detections", "objects", "boxes"):
                if key in model_out and isinstance(model_out[key], (list, tuple, np.ndarray)):
                    return list(model_out[key])
            # Single dict that may itself be a detection
            return [model_out]

        # If list/tuple/ndarray: expand to list
        if isinstance(model_out, (list, tuple)):
            return list(model_out)

        if isinstance(model_out, np.ndarray):
            if model_out.ndim == 2 and model_out.shape[1] in (4, 5):
                return [row for row in model_out]
            if model_out.ndim == 1 and model_out.size >= 4:
                return [model_out]
            # fallback: try to convert rows
            try:
                return [row for row in model_out]
            except Exception:
                return [model_out]

        # Unknown object: wrap
        return [model_out]

    def _extract_bbox_score_label(self, item: Any) -> Tuple[Optional[Any], Optional[float], Optional[str]]:
        """Try to pull bbox, score, and label from an item."""
        # If it's already a Detection
        if isinstance(item, Detection):
            return item.bbox, item.score, item.label

        # Numpy row or list-like: [x1,y1,x2,y2,(score),(class/label)]
        if isinstance(item, (list, tuple, np.ndarray)):
            arr = np.asarray(item).flatten()
            if arr.size >= 4:
                bbox = arr[:4].tolist()
                score = float(arr[4]) if arr.size >= 5 else None
                label = None
                if arr.size >= 6:
                    label = str(arr[5])
                return bbox, score, label
            return None, None, None

        # Dict-like
        if isinstance(item, dict):
            # Score keys
            score = None
            for k in ("score", "confidence", "prob", "conf"):
                if k in item:
                    try:
                        score = float(item[k])
                    except Exception:
                        score = None
                    break
            # Label/class keys
            label = None
            for k in ("label", "class", "class_name", "category"):
                if k in item:
                    label = str(item[k])
                    break
            # bbox keys
            if "bbox" in item:
                return item["bbox"], score, label
            if "box" in item:
                return item["box"], score, label
            # individual fields
            if all(k in item for k in ("x1", "y1", "x2", "y2")):
                return (item["x1"], item["y1"], item["x2"], item["y2"]), score, label
            if all(k in item for k in ("x", "y", "w", "h")):
                return (item["x"], item["y"], item["x"] + item["w"], item["y"] + item["h"]), score, label
            # Sometimes a single dict holds nested detections (handled earlier) - otherwise unknown
            return None, score, label

        # Unknown item
        return None, None, None

    def _to_xyxy_pixels(self, bbox_raw: Any, frame_w: int, frame_h: int) -> Tuple[int, int, int, int]:
        """Convert bbox in raw form to absolute pixel (x1,y1,x2,y2)."""
        arr = np.asarray(bbox_raw, dtype=float).flatten()
        if arr.size < 4:
            raise ValueError("bbox must have at least 4 numeric values")

        # If they look normalized (all values in 0..1.05) treat as normalized xyxy
        if np.all(arr[:4] >= 0.0) and np.all(arr[:4] <= 1.05):
            x1 = int(round(float(arr[0]) * frame_w))
            y1 = int(round(float(arr[1]) * frame_h))
            x2 = int(round(float(arr[2]) * frame_w))
            y2 = int(round(float(arr[3]) * frame_h))
            return x1, y1, x2, y2

        # Otherwise interpret as absolute coords, but detect xywh if arr[2] < arr[0] (common when models return x,w)
        x0, y0, x2_or_w, y2_or_h = float(arr[0]), float(arr[1]), float(arr[2]), float(arr[3])
        # Heuristic: if x2_or_w < x0 or y2_or_h < y0 -> treat as x,y,w,h
        if (x2_or_w < x0) or (y2_or_h < y0):
            x1 = int(round(x0))
            y1 = int(round(y0))
            x2 = int(round(x0 + x2_or_w))
            y2 = int(round(y0 + y2_or_h))
            return x1, y1, x2, y2

        # Otherwise assume x1,y1,x2,y2 absolute
        x1 = int(round(x0))
        y1 = int(round(y0))
        x2 = int(round(x2_or_w))
        y2 = int(round(y2_or_h))
        return x1, y1, x2, y2

    def _label_color(self, label: Optional[str]) -> Tuple[int, int, int]:
        """Deterministic color for a label. Returns a BGR tuple for OpenCV drawing."""
        if not label:
            # green-ish default
            return (0, 200, 0)
        h = abs(hash(label)) % 360
        # convert hue to BGR via simple HSV->BGR (approx)
        r, g, b = colorsys.hsv_to_rgb(h / 360.0, 0.7, 0.9)
        # Convert 0..1 -> 0..255 and BGR ordering
        return (int(b * 255), int(g * 255), int(r * 255))
