import os
import degirum as dg

# LOCAL inference, with local model zoo
zoo_path = os.path.expanduser("~/degirum-zoo")

# Optional: list available models to validate your names
models = dg.list_models(dg.LOCAL, zoo_url=zoo_path, token=None)
print("Local models:", models)

# Load your detector and embedder models
detector = dg.load_model(
    model_name="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
    inference_host_address=dg.LOCAL,
    zoo_url=zoo_path,
    token=None
)

embedder = dg.load_model(
    model_name="arcface_mobilefacenet--112x112_quant_hailort_hailo8l_1",
    inference_host_address=dg.LOCAL,
    zoo_url=zoo_path,
    token=None
)


import os
import degirum as dg

# Point to your local model zoo
zoo_url = os.path.expanduser("~/degirum-zoo")

# Optional: list available local models to validate names
models = dg.list_models(dg.LOCAL, zoo_url=zoo_url, token=None)
print("Local models available:", models)

# # Example of model init inside pipeline
# detector = AiSimpleGizmo(
#     model=detector_model,
#     zoo_url=zoo_url,
#     inference_host_address=dg.LOCAL,  # ensure local inference
#     stream_depth=stream_depth,
#     allow_drop=allow_drop,
# )
