"""
Ray VPF Processors Module

This module contains frame processors and supervision integration.
"""

from .base_processor import BaseFrameProcessor
from .supervision_integration import (
    SupervisionConverter,
    SupervisionTracker,
    EnhancedFrameProcessor
)
from .advanced_processors import AdvancedFaceTrackingProcessor
from .utils import (
    convert_detections_to_supervision,
    create_hailo_showcase_processor,
    create_face_tracking_processor,
    create_face_tracking_processor_preset,
    DetectionSourceFactory
)

# Check supervision availability
try:
    import supervision as sv
    SUPERVISION_AVAILABLE = True
except ImportError:
    sv = None
    SUPERVISION_AVAILABLE = False

__all__ = [
    'BaseFrameProcessor',
    'SupervisionConverter',
    'SupervisionTracker',
    'EnhancedFrameProcessor',
    'AdvancedFaceTrackingProcessor',
    'convert_detections_to_supervision',
    'create_hailo_showcase_processor',
    'create_face_tracking_processor',
    'create_face_tracking_processor_preset',
    'DetectionSourceFactory',
    'SUPERVISION_AVAILABLE'
]
