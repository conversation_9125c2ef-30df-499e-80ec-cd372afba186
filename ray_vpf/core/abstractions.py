"""
Abstract base classes for Ray VPF framework.

These classes define the core interfaces that all components must implement.
"""

import abc
from typing import Optional, List, Iterable
import numpy as np

from .data_structures import FramePacket, Detection


class FrameSource(abc.ABC):
    """Abstract frame provider.

    Implementations must provide `read()` to return RGB numpy frames (HxWx3) or None on EOF.
    """

    @abc.abstractmethod
    def read(self) -> Optional[np.ndarray]:
        """Read the next frame from the source.
        
        Returns:
            RGB frame as numpy array (HxWx3) or None if no more frames
        """
        pass

    @abc.abstractmethod
    def start(self) -> None:
        """Start the frame source."""
        pass

    @abc.abstractmethod
    def stop(self) -> None:
        """Stop the frame source."""
        pass


class InferenceModel(abc.ABC):
    """Abstract inference model.

    Implement `infer(frame_rgb)` and return model-specific output. The framework will
    normalize that output into `Detection` objects for processors via helpers.
    """

    @abc.abstractmethod
    def infer(self, frame_rgb: np.ndarray) -> any:
        """Run inference on a frame.
        
        Args:
            frame_rgb: Input frame as RGB numpy array
            
        Returns:
            Model-specific output
        """
        pass

    def warmup(self, frame_rgb: np.ndarray) -> None:
        """Optional warmup - default is no-op.
        
        Args:
            frame_rgb: Sample frame for warmup
        """
        return


class FrameProcessor(abc.ABC):
    """Business-logic class. Implement `process(packet)` to return processed RGB frame.

    The base class provides helpers: `parse_detections` and `draw_boxes` to avoid
    reimplementing parsing/drawing across showcases.
    """

    def __init__(self, conf_threshold: float = 0.0):
        self.conf_threshold = float(conf_threshold or 0.0)

    @abc.abstractmethod
    def process(self, pkt: FramePacket) -> np.ndarray:
        """Process a frame packet and return processed RGB frame.
        
        Args:
            pkt: FramePacket containing frame and model output
            
        Returns:
            Processed RGB frame (HxWx3 uint8)
        """
        pass


class FrameSink(abc.ABC):
    """Abstract sink - e.g. file writer, RTSP, cloud upload."""

    @abc.abstractmethod
    def start(self) -> None:
        """Start the sink."""
        pass

    @abc.abstractmethod
    def put(self, frame_rgb: np.ndarray) -> None:
        """Write a frame to the sink.
        
        Args:
            frame_rgb: RGB frame to write
        """
        pass

    @abc.abstractmethod
    def stop(self) -> None:
        """Stop the sink."""
        pass
