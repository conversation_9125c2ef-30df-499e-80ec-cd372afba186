"""
Camera source implementations for Ray VPF.
"""

import time
import logging
from typing import Optional, Tuple

import cv2
import numpy as np

from ..core import FrameSource

logger = logging.getLogger(__name__)


class Picamera2Source(FrameSource):
    """FrameSource using Picamera2 on Raspberry Pi.
    Outputs RGB frames (HxWx3 uint8).
    """

    def __init__(self, size=(1280, 720), format="RGB888"):
        try:
            from picamera2 import Picamera2
        except ImportError:
            raise ImportError("picamera2 library not available. Install with: pip install picamera2")

        self.size = size
        self.format = format
        self.picam2 = Picamera2()
        self._stopped = True

        # Configure camera
        config = self.picam2.create_preview_configuration(
            main={"size": self.size, "format": self.format}
        )
        self.picam2.configure(config)

    def start(self):
        """Start the Picamera2 source."""
        self.picam2.start()
        self._stopped = False
        logger.info("Picamera2Source started: size=%s format=%s", self.size, self.format)

    def read(self) -> Optional[np.ndarray]:
        """Read a frame from the camera."""
        if self._stopped:
            return None
        try:
            frame = self.picam2.capture_array()
            # Already in RGB888 format
            return frame
        except Exception as e:
            logger.error("Picamera2 capture error: %s", e)
            return None

    def stop(self):
        """Stop the Picamera2 source."""
        self._stopped = True
        try:
            if hasattr(self, 'picam2') and self.picam2 is not None:
                self.picam2.stop()
                # Give camera time to stop cleanly
                time.sleep(0.1)
        except Exception as e:
            logger.debug(f"Picamera2 stop error: {e}")
        try:
            if hasattr(self, 'picam2') and self.picam2 is not None:
                self.picam2.close()
                self.picam2 = None
        except Exception as e:
            logger.debug(f"Picamera2 close error: {e}")
        logger.info("Picamera2Source stopped")


class WebcamSource(FrameSource):
    """Enhanced webcam source with robust camera detection and initialization."""

    def __init__(self, camera_index: int = 0, size: Optional[Tuple[int, int]] = None, auto_detect: bool = True):
        self.camera_index = camera_index
        self.size = size or (640, 480)
        self.auto_detect = auto_detect
        self._cap = None
        self._stopped = True
        self._actual_camera_index = None
        self._frame_count = 0
        self._error_count = 0

    def start(self):
        """Start the webcam source with robust camera detection."""
        if self.auto_detect:
            self._actual_camera_index = self._find_working_camera()
        else:
            self._actual_camera_index = self.camera_index

        if self._actual_camera_index is None:
            raise RuntimeError("No working camera found")

        # Try different backends for better compatibility
        backends = [cv2.CAP_V4L2, cv2.CAP_ANY]

        for backend in backends:
            try:
                logger.debug(f"Trying camera {self._actual_camera_index} with backend {backend}")
                self._cap = cv2.VideoCapture(self._actual_camera_index, backend)

                if not self._cap.isOpened():
                    continue

                # Configure camera properties
                self._configure_camera()

                # Test if we can actually read frames
                ret, test_frame = self._cap.read()
                if ret and test_frame is not None:
                    logger.info(f"WebcamSource started: camera {self._actual_camera_index} with backend {backend}")
                    logger.info(f"Camera resolution: {test_frame.shape[1]}x{test_frame.shape[0]}")
                    self._stopped = False
                    return
                else:
                    logger.debug(f"Camera {self._actual_camera_index} opened but no frames")
                    self._cap.release()
                    self._cap = None

            except Exception as e:
                logger.debug(f"Failed to open camera {self._actual_camera_index} with backend {backend}: {e}")
                if self._cap:
                    self._cap.release()
                    self._cap = None

        raise RuntimeError(f"Cannot initialize camera {self._actual_camera_index}")

    def _find_working_camera(self) -> Optional[int]:
        """Find a working camera by testing multiple indices."""
        logger.info("Auto-detecting working camera...")

        # Test common camera indices
        test_indices = [0, 1, 2, 3, 4, 5, 6, 7]

        for idx in test_indices:
            try:
                logger.debug(f"Testing camera index {idx}")
                cap = cv2.VideoCapture(idx)

                if cap.isOpened():
                    # Try to read a frame
                    ret, frame = cap.read()
                    cap.release()

                    if ret and frame is not None:
                        logger.info(f"Found working camera at index {idx}")
                        return idx
                    else:
                        logger.debug(f"Camera {idx} opened but no frame")
                else:
                    logger.debug(f"Camera {idx} cannot be opened")

            except Exception as e:
                logger.debug(f"Error testing camera {idx}: {e}")

        logger.warning("No working camera found")
        return None

    def _configure_camera(self):
        """Configure camera properties for optimal performance."""
        if not self._cap:
            return

        try:
            # Set resolution
            self._cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.size[0])
            self._cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.size[1])

            # Set FPS
            self._cap.set(cv2.CAP_PROP_FPS, 30)

            # Set buffer size to reduce latency
            self._cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            # Auto exposure and white balance
            self._cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # Auto exposure

            # Get actual properties
            actual_width = int(self._cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self._cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self._cap.get(cv2.CAP_PROP_FPS)

            logger.debug(f"Camera configured: {actual_width}x{actual_height} @ {actual_fps} FPS")

        except Exception as e:
            logger.warning(f"Failed to configure camera properties: {e}")

    def read(self) -> Optional[np.ndarray]:
        """Read a frame from the webcam with error handling."""
        if self._cap is None or self._stopped:
            return None

        try:
            ret, frame_bgr = self._cap.read()
            if not ret or frame_bgr is None:
                self._error_count += 1
                if self._error_count > 10:
                    logger.error("Too many consecutive read errors, stopping camera")
                    self._stopped = True
                return None

            # Reset error count on successful read
            self._error_count = 0
            self._frame_count += 1

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)

            # Resize if needed
            if frame_rgb.shape[:2] != (self.size[1], self.size[0]):
                frame_rgb = cv2.resize(frame_rgb, self.size)

            return frame_rgb

        except Exception as e:
            logger.error(f"Error reading frame: {e}")
            self._error_count += 1
            return None

    def stop(self):
        """Stop the webcam source."""
        self._stopped = True
        if self._cap is not None:
            try:
                self._cap.release()
            except Exception as e:
                logger.debug(f"Error releasing camera: {e}")
            finally:
                self._cap = None

        logger.info(f"WebcamSource stopped: {self._frame_count} frames read, {self._error_count} errors")

    def get_stats(self) -> dict:
        """Get camera statistics."""
        return {
            'camera_index': self._actual_camera_index,
            'frames_read': self._frame_count,
            'error_count': self._error_count,
            'is_stopped': self._stopped
        }
