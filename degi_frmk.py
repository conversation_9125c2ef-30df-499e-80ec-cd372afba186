#!/usr/bin/env python3
"""
face_pipeline_fixed.py
RPi5 + Hailo-8L face recognition pipeline (local-only zoo: ~/degirum-zoo)

- Uses local model zoo (no cloud token)
- Loads models as objects and attempts to attach ObjectSelector analyzer
- Falls back to manual top-K selection if analyzer isn't available
- Threaded, non-blocking Streams pipeline (Video -> Detector -> Crop -> Embed -> Combine -> Sink)
"""

import os
import sys
import time
import logging
import argparse
import queue
import threading
from typing import Optional, Tuple, List

import cv2
import numpy as np
import degirum as dg

# Try to import degirum_tools (optional)
try:
    import degirum_tools
    from degirum_tools import ObjectSelector  # convenience if available
    HAS_DEGIRUM_TOOLS = True
except Exception:
    degirum_tools = None
    ObjectSelector = None
    HAS_DEGIRUM_TOOLS = False

# Streams gizmos
from degirum_tools.streams import (
    Composition,
    VideoSourceGizmo,
    VideoDisplayGizmo,
    AiSimpleGizmo,
    AiObjectDetectionCroppingGizmo,
    CropC<PERSON>iningGiz<PERSON>,
    Sink<PERSON><PERSON><PERSON>,
)

# ---------- Config defaults ----------
DEFAULT_ZOO = os.path.expanduser("~/degirum-zoo")
DEFAULT_DETECTOR = "scrfd_10g--640x640_quant_hailort_hailo8l_1"
DEFAULT_EMBEDDER = "arcface_mobilefacenet--112x112_quant_hailort_hailo8l_1"

# ---------- Utilities ----------
def find_embedding_in_result(entry: dict) -> Optional[np.ndarray]:
    """Robustly find an embedding vector in a detection result dict."""
    # common keys
    for k in ("embedding", "features", "face_embedding", "fc1", "output"):
        v = entry.get(k)
        if v is None:
            continue
        # accept lists / tuples / ndarrays
        if isinstance(v, (list, tuple, np.ndarray)):
            arr = np.array(v, dtype=np.float32).reshape(-1)
            if arr.size >= 32:  # heuristic min dimension
                return arr
    # fallback: find the first numeric-like list/ndarray with length >= 32
    for k, v in entry.items():
        if isinstance(v, (list, tuple, np.ndarray)):
            arr = np.array(v, dtype=np.float32).reshape(-1)
            if arr.size >= 32:
                return arr
    return None


def cosine_distance(a: np.ndarray, b: np.ndarray) -> float:
    if a is None or b is None or a.size == 0 or b.size == 0:
        return 1.0
    na = a / (np.linalg.norm(a) + 1e-10)
    nb = b / (np.linalg.norm(b) + 1e-10)
    sim = float(np.dot(na, nb))
    return 1.0 - sim


class SimpleDiskDB:
    """Simple filesystem DB: each embedding saved as numpy .npy with name metadata."""
    def __init__(self, folder: str):
        self.folder = folder
        os.makedirs(self.folder, exist_ok=True)
        self._index = []  # list of tuples (name, path)
        self._load_index()

    def _load_index(self):
        idxf = os.path.join(self.folder, "index.txt")
        if os.path.exists(idxf):
            with open(idxf, "r") as f:
                for ln in f:
                    name, fname = ln.strip().split("\t")
                    self._index.append((name, fname))

    def add(self, name: str, emb: np.ndarray):
        ts = int(time.time() * 1000)
        fname = f"{name}_{ts}.npy"
        path = os.path.join(self.folder, fname)
        np.save(path, emb.astype(np.float32))
        self._index.append((name, fname))
        with open(os.path.join(self.folder, "index.txt"), "a") as f:
            f.write(f"{name}\t{fname}\n")

    def all(self):
        out = []
        for name, fname in self._index:
            path = os.path.join(self.folder, fname)
            if os.path.exists(path):
                arr = np.load(path)
                out.append((name, arr))
        return out


# ---------- Main pipeline ----------
def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--video", default="0", help="OpenCV source index or path (default 0)")
    ap.add_argument("--zoo", default=DEFAULT_ZOO, help="Local degirum zoo directory (default ~/degirum-zoo)")
    ap.add_argument("--detector", default=DEFAULT_DETECTOR, help="Detector model name (from local zoo)")
    ap.add_argument("--embedder", default=DEFAULT_EMBEDDER, help="Embedder model name")
    ap.add_argument("--threshold", type=float, default=0.55, help="cosine distance threshold (<= match)")
    ap.add_argument("--topk", type=int, default=1, help="Top-K faces to process with embedder")
    ap.add_argument("--db", default="./face_db", help="Folder to store embeddings (.npy)")
    args = ap.parse_args()

    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
    logging.info("Face pipeline starting (local zoo: %s)", args.zoo)

    zoo_url = os.path.expanduser(args.zoo)
    detector_name = args.detector
    embedder_name = args.embedder
    threshold = args.threshold
    top_k = max(1, int(args.topk))

    # Convert video source
    video_src = int(args.video) if str(args.video).isdigit() else args.video

    # Validate local zoo listing (best-effort)
    try:
        local_models = dg.list_models(dg.LOCAL, zoo_url=zoo_url, token=None)
        logging.info("Local models available: %s", list(local_models.keys()) if isinstance(local_models, dict) else str(local_models)[:200])
    except Exception as e:
        logging.warning("Could not list local models from zoo '%s': %s", zoo_url, e)

    # Prepare DB and writer thread
    db = SimpleDiskDB(args.db)
    enroll_q = queue.Queue(maxsize=8)

    def db_writer():
        logging.info("DB writer thread started")
        while True:
            try:
                name, emb = enroll_q.get()
            except Exception:
                break
            try:
                db.add(name, emb)
                logging.info("Enrolled %s", name)
            except Exception as ex:
                logging.exception("DB write failed: %s", ex)
            finally:
                enroll_q.task_done()

    t_writer = threading.Thread(target=db_writer, daemon=True)
    t_writer.start()

    # Build pipeline
    try:
        video = VideoSourceGizmo(video_source=video_src, stop_composition_on_end=False)

        # Load detector model object (local)
        logging.info("Loading detector model '%s' from local zoo '%s' ...", detector_name, zoo_url)
        detector_model = dg.load_model(
            detector_name,
            inference_host_address=dg.LOCAL,
            zoo_url=zoo_url,
            token=None,
        )

        # Try to attach ObjectSelector analyzer if available
        selector_attached = False
        if HAS_DEGIRUM_TOOLS and hasattr(degirum_tools, "ObjectSelectionStrategies"):
            try:
                selector = degirum_tools.ObjectSelector(
                    top_k=top_k,
                    selection_strategy=degirum_tools.ObjectSelectionStrategies.HIGHEST_SCORE,
                    use_tracking=False,
                    show_overlay=False,
                )
                # attach analyzer to model object (model.attach_analyzers supported)
                try:
                    # model.attach_analyzers is supported by PySDK models
                    detector_model.attach_analyzers(selector)
                    selector_attached = True
                    logging.info("Attached ObjectSelector analyzer to detector model (top_k=%d)", top_k)
                except Exception:
                    # fallback: degirum_tools.attach_analyzers(model, [selector]) sometimes available
                    try:
                        degirum_tools.attach_analyzers(detector_model, [selector])
                        selector_attached = True
                        logging.info("Attached ObjectSelector (via degirum_tools.attach_analyzers)")
                    except Exception:
                        logging.warning("Could not attach ObjectSelector analyzer; will use manual top-K selection")
            except Exception:
                logging.warning("ObjectSelector creation failed; will use manual top-K selection")
        else:
            logging.info("degirum_tools/ObjectSelector not available; will use manual top-K selection")

        # detector gizmo uses model object
        detector = AiSimpleGizmo(
            model=detector_model,
            stream_depth=6,
            allow_drop=True,
        )

        # cropper (only label 'face')
        cropper = AiObjectDetectionCroppingGizmo(labels=["face"], crop_extent=0.28, crop_aspect_ratio=1.0, stream_depth=6, allow_drop=True)

        # load embedder model object (local)
        logging.info("Loading embedder model '%s' ...", embedder_name)
        embedder_model = dg.load_model(
            embedder_name,
            inference_host_address=dg.LOCAL,
            zoo_url=zoo_url,
            token=None,
        )
        embedder = AiSimpleGizmo(model=embedder_model, stream_depth=6, allow_drop=True)

        combiner = CropCombiningGizmo()
        sink = SinkGizmo(stream_depth=8)
        display = VideoDisplayGizmo(window_titles="FaceRecognition (fixed)", show_fps=True)

        comp = Composition(video, detector, cropper, embedder, combiner, sink, display)
        # wire pipeline
        video >> detector >> cropper >> embedder >> combiner >> sink
        combiner >> display

        logging.info("Starting composition ...")
        comp.start(wait=False)
    except Exception as e:
        logging.exception("Failed building composition: %s", e)
        sys.exit(1)

    # Main loop: consume combined results, perform matching & enroll
    try:
        while True:
            item = sink.get(timeout_s=1.0)
            if item is None:
                if cv2.waitKey(1) & 0xFF in (ord("q"), ord("Q")):
                    break
                continue

            frame = item.image
            results_list = None
            # find detector results (first result list that contains 'bbox')
            if "results" in item.meta:
                for k, res in item.meta["results"].items():
                    if hasattr(res, "results") and isinstance(res.results, list) and len(res.results) > 0:
                        if "bbox" in res.results[0]:
                            results_list = res.results
                            break

            labels = {}
            # If selector was attached, the detector results should already be filtered; otherwise apply manual top-k
            if results_list:
                # Manual top-k if analyzer wasn't attached
                selected_indices = None
                if not selector_attached:
                    order = sorted(range(len(results_list)), key=lambda i: results_list[i].get("score", 0.0), reverse=True)
                    selected_indices = order[:top_k]
                    selected = [results_list[i] for i in selected_indices]
                else:
                    # assume results_list already filtered by analyzer (but still guard)
                    selected = results_list[:top_k]

                # For each selected detection, try to read embedding (attached by combiner)
                for sel_idx, r in enumerate(selected):
                    emb = find_embedding_in_result(r)
                    if emb is not None:
                        # match against DB
                        best_name, best_dist = match_against_db(db, emb, threshold)
                        if best_name:
                            labels[sel_idx] = f"{best_name} ({best_dist:.2f})"
                        else:
                            labels[sel_idx] = f"Unknown ({best_dist:.2f})"
                    else:
                        labels[sel_idx] = "NoEmb"

                # Draw selected boxes only (map selected -> draw)
                vis = draw_selected(frame, selected, labels)
            else:
                vis = frame

            cv2.imshow("FaceRecognition (fixed)", vis)

            key = cv2.waitKey(1) & 0xFF
            if key in (ord("q"), ord("Q")):
                logging.info("Quit pressed")
                break
            if key in (ord("e"), ord("E")):
                # enroll highest-score detection from last results_list
                if not results_list or len(results_list) == 0:
                    logging.info("No detections to enroll")
                    continue
                best_idx = max(range(len(results_list)), key=lambda i: results_list[i].get("score", 0.0))
                r = results_list[best_idx]
                emb = find_embedding_in_result(r)
                if emb is None:
                    logging.warning("No embedding present for selected face")
                    continue
                name = input("Name to enroll: ").strip()
                if not name:
                    logging.info("Empty name; cancel")
                    continue
                try:
                    enroll_q.put_nowait((name, emb))
                    logging.info("Queued enrollment for '%s'", name)
                except queue.Full:
                    logging.warning("Enrollment queue full; try again")

    except KeyboardInterrupt:
        logging.info("Interrupted by user")
    finally:
        # Stop composition & writer
        logging.info("Stopping composition")
        try:
            comp.stop()
        except Exception:
            pass
        # drain enroll queue then exit
        try:
            enroll_q.join(timeout=2.0)
        except Exception:
            pass
        logging.info("Exiting")
        cv2.destroyAllWindows()


# ---------- helpers for matching & drawing ----------
def match_against_db(db: SimpleDiskDB, emb: np.ndarray, threshold: float) -> Tuple[Optional[str], float]:
    best_name = None
    best_dist = 1.0
    for name, ref in db.all():
        d = cosine_distance(emb, ref)
        if d < best_dist:
            best_dist = d
            best_name = name
    if best_name is None or best_dist > threshold:
        return None, best_dist
    return best_name, best_dist


def draw_selected(frame: np.ndarray, selected_results: List[dict], labels: dict) -> np.ndarray:
    out = frame.copy()
    for i, r in enumerate(selected_results):
        bbox = r.get("bbox")
        if not bbox:
            continue
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(out, (x1, y1), (x2, y2), (0, 220, 0), 2)
        label = labels.get(i, f"{r.get('score', 0.0):.2f}")
        cv2.putText(out, label, (x1, max(0, y1 - 6)), cv2.FONT_HERSHEY_SIMPLEX, 0.55, (0, 220, 0), 2, cv2.LINE_AA)
    return out


if __name__ == "__main__":
    main()
