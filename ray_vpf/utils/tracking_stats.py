"""
Face tracking statistics and monitoring utilities for Ray VPF.
"""

import time
import threading
import logging
from collections import defaultdict, deque
from typing import Dict, List, Any

logger = logging.getLogger(__name__)


class FaceTrackingStats:
    """
    Thread-safe statistics tracker for face detection and tracking performance.
    
    This class provides comprehensive tracking of face detection and tracking
    metrics including FPS, detection rates, track durations, and more.
    """
    
    def __init__(self):
        self._lock = threading.Lock()
        self.reset()

    def reset(self):
        """Reset all statistics."""
        with self._lock:
            self.total_frames = 0
            self.frames_with_faces = 0
            self.total_detections = 0
            self.active_tracks = 0
            self.max_concurrent_faces = 0
            self.track_durations = defaultdict(int)  # track_id -> frame_count
            self.fps_history = deque(maxlen=30)
            self.start_time = time.time()
            self.last_frame_time = time.time()
    
    def update_frame(self, num_faces: int, active_track_ids: List[int]):
        """
        Update statistics for a new frame.
        
        Args:
            num_faces: Number of faces detected in this frame
            active_track_ids: List of active tracking IDs
        """
        with self._lock:
            current_time = time.time()
            
            # Frame statistics
            self.total_frames += 1
            if num_faces > 0:
                self.frames_with_faces += 1
                self.total_detections += num_faces
            
            # Tracking statistics
            self.active_tracks = len(active_track_ids)
            self.max_concurrent_faces = max(self.max_concurrent_faces, num_faces)
            
            # Update track durations
            for track_id in active_track_ids:
                self.track_durations[track_id] += 1
            
            # FPS calculation
            if self.last_frame_time > 0:
                frame_time = current_time - self.last_frame_time
                if frame_time > 0:
                    fps = 1.0 / frame_time
                    self.fps_history.append(fps)
            
            self.last_frame_time = current_time
    
    def get_current_fps(self) -> float:
        """Get current FPS based on recent frames."""
        with self._lock:
            if len(self.fps_history) > 0:
                return sum(self.fps_history) / len(self.fps_history)
            return 0.0
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive statistics summary."""
        with self._lock:
            runtime = time.time() - self.start_time
            avg_fps = self.total_frames / max(runtime, 0.001)

            # Calculate current FPS without recursive lock
            current_fps = 0.0
            if len(self.fps_history) > 0:
                current_fps = sum(self.fps_history) / len(self.fps_history)

            return {
                'runtime': runtime,
                'total_frames': self.total_frames,
                'frames_with_faces': self.frames_with_faces,
                'total_detections': self.total_detections,
                'active_tracks': self.active_tracks,
                'max_concurrent_faces': self.max_concurrent_faces,
                'unique_faces_seen': len(self.track_durations),
                'avg_fps': avg_fps,
                'current_fps': current_fps,
                'face_detection_rate': self.frames_with_faces / max(self.total_frames, 1),
                'avg_detections_per_frame': self.total_detections / max(self.total_frames, 1),
                'longest_track_duration': max(self.track_durations.values()) if self.track_durations else 0
            }

    def get_track_duration(self, track_id: int) -> int:
        """Get duration of a specific track."""
        with self._lock:
            return self.track_durations.get(track_id, 0)

    def get_active_track_count(self) -> int:
        """Get number of currently active tracks."""
        with self._lock:
            return self.active_tracks

    def get_total_unique_faces(self) -> int:
        """Get total number of unique faces seen."""
        with self._lock:
            return len(self.track_durations)


class ConsoleDisplay:
    """
    Console-based display for face tracking statistics.
    
    Provides formatted console output for tracking statistics with
    configurable update intervals and professional formatting.
    """

    def __init__(self, update_interval: float = 2.0):
        self.update_interval = update_interval
        self.last_update = 0
        self.frame_count = 0

    def update(self, stats: Dict[str, Any], frame_count: int) -> None:
        """
        Update console display with current statistics.
        
        Args:
            stats: Statistics dictionary from FaceTrackingStats
            frame_count: Current frame count
        """
        current_time = time.time()
        self.frame_count = frame_count

        if current_time - self.last_update >= self.update_interval:
            self._print_stats(stats)
            self.last_update = current_time

    def _print_stats(self, stats: Dict[str, Any]) -> None:
        """Print formatted statistics to console."""
        # Clear previous lines (simple approach)
        print("\n" + "="*60)

        # Check if this is face tracking stats or basic stats
        is_face_tracking = 'active_tracks' in stats or 'unique_faces_seen' in stats

        if is_face_tracking:
            print("FACE TRACKING STATISTICS")
        else:
            print("PROCESSING STATISTICS")

        print("="*60)
        print(f"Runtime: {stats.get('runtime', 0):.1f}s | Frames: {stats.get('total_frames', 0)}")
        print(f"Current FPS: {stats.get('current_fps', 0):.1f} | Average FPS: {stats.get('avg_fps', 0):.1f}")

        if is_face_tracking:
            print(f"Active Faces: {stats.get('active_tracks', 0)} | Total Unique: {stats.get('unique_faces_seen', 0)}")
            print(f"Detection Rate: {stats.get('face_detection_rate', 0):.1%} | Max Concurrent: {stats.get('max_concurrent_faces', 0)}")
            longest_track = stats.get('longest_track_duration', 0)
            if longest_track > 0:
                print(f"Longest Track: {longest_track} frames")
        else:
            # Basic processing stats
            detections = stats.get('current_detections', 0)
            print(f"Current Detections: {detections}")

        print("="*60)
        print("Press Ctrl+C to stop...")

    def final_report(self, stats: Dict[str, Any], output_filename: str = None, raw_output_filename: str = None) -> None:
        """
        Print final statistics report.

        Args:
            stats: Final statistics dictionary
            output_filename: Optional output video filename
            raw_output_filename: Optional raw output video filename
        """
        print("\n" + "="*60)

        # Check if this is face tracking stats or basic stats
        is_face_tracking = 'unique_faces_seen' in stats or 'active_tracks' in stats

        if is_face_tracking:
            print("FINAL FACE TRACKING REPORT")
        else:
            print("FINAL PROCESSING REPORT")

        print("="*60)
        print(f"Total Runtime: {stats.get('runtime', 0):.1f} seconds")
        print(f"Frames Processed: {stats.get('total_frames', 0)}")
        print(f"Average FPS: {stats.get('avg_fps', 0):.1f}")

        # Only show face tracking specific stats if available
        if is_face_tracking:
            print(f"Frames with Faces: {stats.get('frames_with_faces', 0)} ({stats.get('face_detection_rate', 0):.1%})")
            print(f"Total Face Detections: {stats.get('total_detections', 0)}")
            print(f"Unique Faces Tracked: {stats.get('unique_faces_seen', 0)}")
            print(f"Maximum Concurrent Faces: {stats.get('max_concurrent_faces', 0)}")
            longest_track = stats.get('longest_track_duration', 0)
            if longest_track > 0:
                print(f"Longest Track Duration: {longest_track} frames")

        # Show processing errors if any
        if 'processing_errors' in stats and stats['processing_errors'] > 0:
            print(f"Processing Errors: {stats['processing_errors']}")

        if output_filename:
            print(f"Output Video Saved: {output_filename}")
        if raw_output_filename:
            print(f"Raw Video Saved: {raw_output_filename}")
        print("="*60)

    def print_performance_summary(self, stats: Dict[str, Any]) -> None:
        """Print a brief performance summary."""
        fps = stats.get('current_fps', 0)
        faces = stats.get('active_tracks', 0)
        total = stats.get('unique_faces_seen', 0)
        print(f"Performance: FPS={fps:.1f}, Faces={faces}, Total={total}")


class PerformanceMonitor:
    """
    Performance monitoring utility for tracking system performance.
    
    Provides detailed performance metrics and can log performance
    data for analysis and optimization.
    """
    
    def __init__(self, log_interval: float = 10.0):
        self.log_interval = log_interval
        self.last_log_time = time.time()
        self.performance_history = deque(maxlen=100)
        
    def update(self, stats: Dict[str, Any]) -> None:
        """Update performance monitoring with new stats."""
        current_time = time.time()
        
        # Store performance data
        self.performance_history.append({
            'timestamp': current_time,
            'fps': stats['current_fps'],
            'active_tracks': stats['active_tracks'],
            'total_frames': stats['total_frames']
        })
        
        # Log performance if interval has passed
        if current_time - self.last_log_time >= self.log_interval:
            self._log_performance(stats)
            self.last_log_time = current_time
    
    def _log_performance(self, stats: Dict[str, Any]) -> None:
        """Log performance statistics."""
        logger.info(f"Performance: FPS={stats['current_fps']:.1f}, "
                   f"Faces={stats['active_tracks']}, "
                   f"Total={stats['unique_faces_seen']}, "
                   f"Detection Rate={stats['face_detection_rate']:.1%}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary over the monitoring period."""
        if not self.performance_history:
            return {}
        
        fps_values = [entry['fps'] for entry in self.performance_history]
        track_counts = [entry['active_tracks'] for entry in self.performance_history]
        
        return {
            'avg_fps': sum(fps_values) / len(fps_values),
            'min_fps': min(fps_values),
            'max_fps': max(fps_values),
            'avg_tracks': sum(track_counts) / len(track_counts),
            'max_tracks': max(track_counts),
            'samples': len(self.performance_history)
        }
