"""
Dummy model implementation for testing Ray VPF.
"""

from typing import Any

import numpy as np

from ..core import InferenceModel


class DummyModel(InferenceModel):
    """A tiny model that returns synthetic detections for testing/showcase."""

    def infer(self, frame_rgb: np.n<PERSON>ray) -> Any:
        """Generate dummy detections for testing."""
        h, w = frame_rgb.shape[:2]
        # produce two boxes: center and top-left
        cx, cy = w // 2, h // 2
        box1 = [cx - w * 0.15, cy - h * 0.15, cx + w * 0.15, cy + h * 0.15]
        box2 = [w * 0.1, h * 0.1, w * 0.3, h * 0.3]
        return [
            {"bbox": box1, "score": 0.9, "label": "dummy_center"},
            {"bbox": box2, "score": 0.6, "label": "dummy_tl"},
        ]
