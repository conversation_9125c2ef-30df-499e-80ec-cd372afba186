"""
Ray VPF (Video Processing Framework)

A simple, powerful, and reusable video processing framework designed for easy
integration and rapid development of video analytics applications.

Key Features:
- KISS principle: Keep It Simple, Stupid
- Object-oriented design with SOLID principles
- Easy to use and extend
- Production-ready components
- Supervision library integration for advanced tracking
- Support for multiple video sources and models

Basic Usage:
    from ray_vpf import VideoFileSource, DummyModel, BaseFrameProcessor, BufferedPipeline
    
    # Create components
    source = VideoFileSource("input.mp4")
    model = DummyModel()
    processor = BaseFrameProcessor()
    
    # Create and run pipeline
    pipeline = BufferedPipeline(source, model, processor)
    pipeline.start()
"""

# Core abstractions
from .core import (
    FrameSource,
    InferenceModel,
    FrameProcessor,
    FrameSink,
    Detection,
    FramePacket
)

# Video sources
from .sources import (
    VideoFileSource,
    Picamera2Source,
    WebcamSource
)

# Models
from .models import (
    DegirumRetinaFaceModel,
    DummyModel,
    FaceDetectionModelAdapter,
    create_face_detection_model
)

# Processors
from .processors import (
    BaseFrameProcessor,
    SupervisionConverter,
    SupervisionTracker,
    EnhancedFrameProcessor,
    AdvancedFaceTrackingProcessor,
    convert_detections_to_supervision,
    create_hailo_showcase_processor,
    create_face_tracking_processor,
    create_face_tracking_processor_preset,
    DetectionSourceFactory,
    SUPERVISION_AVAILABLE
)

# Sinks
from .sinks import (
    OpenCVFileSink
)

# Pipeline
from .pipeline import (
    BufferedPipeline
)

# Utils
from .utils import (
    setup_logging,
    FaceTrackingStats,
    ConsoleDisplay,
    PerformanceMonitor,
    TrackingTrailsVisualizer,
    TrailsConfiguration,
    StatsOverlay,
    FrameAnnotator,
    PerformanceVisualizer
)

__version__ = "1.0.0"
__author__ = "Ray VPF Team"
__description__ = "Simple and powerful video processing framework"

__all__ = [
    # Core
    'FrameSource',
    'InferenceModel', 
    'FrameProcessor',
    'FrameSink',
    'Detection',
    'FramePacket',
    
    # Sources
    'VideoFileSource',
    'Picamera2Source',
    'WebcamSource',
    
    # Models
    'DegirumRetinaFaceModel',
    'DummyModel',
    'FaceDetectionModelAdapter',
    'create_face_detection_model',
    
    # Processors
    'BaseFrameProcessor',
    'SupervisionConverter',
    'SupervisionTracker',
    'EnhancedFrameProcessor',
    'AdvancedFaceTrackingProcessor',
    'convert_detections_to_supervision',
    'create_hailo_showcase_processor',
    'create_face_tracking_processor',
    'create_face_tracking_processor_preset',
    'DetectionSourceFactory',
    'SUPERVISION_AVAILABLE',
    
    # Sinks
    'OpenCVFileSink',
    
    # Pipeline
    'BufferedPipeline',
    
    # Utils
    'setup_logging',
    'FaceTrackingStats',
    'ConsoleDisplay',
    'PerformanceMonitor',
    'TrackingTrailsVisualizer',
    'TrailsConfiguration',
    'StatsOverlay',
    'FrameAnnotator',
    'PerformanceVisualizer',
    
    # Metadata
    '__version__',
    '__author__',
    '__description__'
]


def get_version():
    """Get the framework version."""
    return __version__


def get_info():
    """Get framework information."""
    return {
        'name': 'Ray VPF',
        'version': __version__,
        'description': __description__,
        'author': __author__,
        'supervision_available': SUPERVISION_AVAILABLE
    }
