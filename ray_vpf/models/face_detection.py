"""
Face detection model implementations for Ray VPF.
"""

import os
import logging
from typing import Optional, Any, List, Dict, Tuple

import numpy as np

from ..core import InferenceModel

logger = logging.getLogger(__name__)


class FaceDetectionModelAdapter(InferenceModel):
    """
    Adaptive face detection model that can work with multiple backends.
    
    This adapter provides a unified interface for different face detection models
    including Hailo/Degirum models, OpenCV models, and fallback dummy models.
    """

    def __init__(
        self,
        model_type: str = 'auto',
        model_name: Optional[str] = None,
        zoo_path: Optional[str] = None,
        confidence_threshold: float = 0.5,
        fallback_to_dummy: bool = True
    ):
        """
        Initialize the face detection model adapter.
        
        Args:
            model_type: Type of model ('hailo', 'degirum', 'opencv', 'dummy', 'auto')
            model_name: Specific model name (for Hailo/Degirum)
            zoo_path: Path to model zoo (for Hailo/Degirum)
            confidence_threshold: Confidence threshold for detections
            fallback_to_dummy: Whether to fallback to dummy model if real model fails
        """
        self.model_type = model_type
        self.model_name = model_name
        self.zoo_path = os.path.expanduser(zoo_path) if zoo_path else None
        self.confidence_threshold = confidence_threshold
        self.fallback_to_dummy = fallback_to_dummy
        
        self.model = None
        self.model_info = "Not loaded"
        self._is_loaded = False
        
        # Model loading will be done lazily in load() or first infer() call
        logger.info(f"FaceDetectionModelAdapter initialized: type={model_type}, "
                   f"model_name={model_name}, threshold={confidence_threshold}")

    def load(self) -> None:
        """Load the face detection model."""
        if self._is_loaded:
            return
            
        if self.model_type == 'auto':
            self._auto_load_model()
        elif self.model_type in ['hailo', 'degirum']:
            self._load_hailo_degirum_model()
        elif self.model_type == 'opencv':
            self._load_opencv_model()
        elif self.model_type == 'dummy':
            self._load_dummy_model()
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
        
        self._is_loaded = True
        logger.info(f"Face detection model loaded: {self.model_info}")

    def _auto_load_model(self) -> None:
        """Automatically detect and load the best available model."""
        # Try Hailo/Degirum first
        try:
            self._load_hailo_degirum_model()
            return
        except Exception as e:
            logger.debug(f"Hailo/Degirum model loading failed: {e}")

        # Try OpenCV models
        try:
            self._load_opencv_model()
            return
        except Exception as e:
            logger.debug(f"OpenCV model loading failed: {e}")

        # Fallback to dummy if enabled
        if self.fallback_to_dummy:
            logger.warning("All real models failed, falling back to dummy model")
            self._load_dummy_model()
        else:
            raise RuntimeError("No face detection models could be loaded")

    def _load_hailo_degirum_model(self) -> None:
        """Load Hailo/Degirum face detection model."""
        try:
            import degirum as dg
        except ImportError:
            raise RuntimeError("Degirum package not available. Install with: pip install degirum")

        # Default model names to try
        default_models = [
            "retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
            "yolov8n_relu6_person--640x640_quant_hailort_multidevice_1",
            "hand_landmark_lite--224x224_quant_hailort_hailo8l_1"
        ]

        models_to_try = [self.model_name] if self.model_name else default_models

        for model_name in models_to_try:
            if model_name is None:
                continue
                
            try:
                logger.info(f"Attempting to load Hailo/Degirum model: {model_name}")
                
                # Connect to zoo
                zoo = dg.connect(dg.LOCAL, self.zoo_path)
                model = zoo.load_model(model_name)
                
                # Set color space if supported
                try:
                    model.input_numpy_colorspace = "RGB"
                except Exception:
                    pass
                
                self.model = model
                self.model_info = f"Hailo/Degirum: {model_name}"
                self.model_type = 'hailo'
                return
                
            except Exception as e:
                logger.debug(f"Failed to load model {model_name}: {e}")
                continue

        raise RuntimeError(f"Failed to load any Hailo/Degirum models. Zoo path: {self.zoo_path}")

    def _load_opencv_model(self) -> None:
        """Load OpenCV face detection model."""
        try:
            import cv2
        except ImportError:
            raise RuntimeError("OpenCV not available")

        # Try to load OpenCV DNN face detection model
        try:
            # You can download these models from OpenCV's repository
            prototxt_path = "opencv_face_detector.pototxt"
            model_path = "opencv_face_detector_uint8.pb"
            
            if os.path.exists(prototxt_path) and os.path.exists(model_path):
                net = cv2.dnn.readNetFromTensorflow(model_path, prototxt_path)
                self.model = net
                self.model_info = "OpenCV DNN Face Detector"
                self.model_type = 'opencv'
                return
        except Exception as e:
            logger.debug(f"OpenCV DNN model loading failed: {e}")

        # Fallback to Haar cascades
        try:
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            if os.path.exists(cascade_path):
                cascade = cv2.CascadeClassifier(cascade_path)
                if not cascade.empty():
                    self.model = cascade
                    self.model_info = "OpenCV Haar Cascade"
                    self.model_type = 'opencv_haar'
                    return
        except Exception as e:
            logger.debug(f"Haar cascade loading failed: {e}")

        raise RuntimeError("No OpenCV face detection models could be loaded")

    def _load_dummy_model(self) -> None:
        """Load dummy model for testing."""
        from .dummy_model import DummyModel
        self.model = DummyModel()
        self.model_info = "Dummy Model (for testing)"
        self.model_type = 'dummy'

    def infer(self, frame_rgb: np.ndarray) -> Any:
        """Run face detection inference on a frame."""
        if not self._is_loaded:
            self.load()

        if self.model is None:
            raise RuntimeError("No model loaded")

        try:
            if self.model_type in ['hailo', 'degirum']:
                return self._infer_hailo_degirum(frame_rgb)
            elif self.model_type == 'opencv':
                return self._infer_opencv_dnn(frame_rgb)
            elif self.model_type == 'opencv_haar':
                return self._infer_opencv_haar(frame_rgb)
            elif self.model_type == 'dummy':
                return self.model.infer(frame_rgb)
            else:
                raise RuntimeError(f"Unknown model type: {self.model_type}")
                
        except Exception as e:
            logger.error(f"Inference failed: {e}")
            if self.fallback_to_dummy and self.model_type != 'dummy':
                logger.warning("Falling back to dummy model for this frame")
                return self._get_dummy_result(frame_rgb)
            raise

    def _infer_hailo_degirum(self, frame_rgb: np.ndarray) -> Any:
        """Run inference with Hailo/Degirum model."""
        return self.model(frame_rgb)

    def _infer_opencv_dnn(self, frame_rgb: np.ndarray) -> List[Dict[str, Any]]:
        """Run inference with OpenCV DNN model."""
        import cv2
        
        h, w = frame_rgb.shape[:2]
        
        # Convert RGB to BGR for OpenCV
        frame_bgr = cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR)
        
        # Create blob
        blob = cv2.dnn.blobFromImage(frame_bgr, 1.0, (300, 300), [104, 117, 123])
        self.model.setInput(blob)
        detections = self.model.forward()
        
        # Parse detections
        results = []
        for i in range(detections.shape[2]):
            confidence = detections[0, 0, i, 2]
            if confidence > self.confidence_threshold:
                x1 = int(detections[0, 0, i, 3] * w)
                y1 = int(detections[0, 0, i, 4] * h)
                x2 = int(detections[0, 0, i, 5] * w)
                y2 = int(detections[0, 0, i, 6] * h)
                
                results.append({
                    'bbox': [x1, y1, x2, y2],
                    'score': float(confidence),
                    'label': 'face'
                })
        
        return results

    def _infer_opencv_haar(self, frame_rgb: np.ndarray) -> List[Dict[str, Any]]:
        """Run inference with OpenCV Haar cascade."""
        import cv2
        
        # Convert to grayscale
        gray = cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2GRAY)
        
        # Detect faces
        faces = self.model.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30)
        )
        
        # Convert to standard format
        results = []
        for (x, y, w, h) in faces:
            results.append({
                'bbox': [x, y, x + w, y + h],
                'score': 0.9,  # Haar cascades don't provide confidence scores
                'label': 'face'
            })
        
        return results

    def _get_dummy_result(self, frame_rgb: np.ndarray) -> List[Dict[str, Any]]:
        """Get dummy result for fallback."""
        from .dummy_model import DummyModel
        dummy = DummyModel()
        return dummy.infer(frame_rgb)

    def warmup(self, frame_rgb: np.ndarray) -> None:
        """Warmup the model."""
        if not self._is_loaded:
            self.load()
            
        try:
            # Run a dummy inference to warm up the model
            _ = self.infer(frame_rgb)
            logger.debug("Model warmup completed")
        except Exception as e:
            logger.warning(f"Model warmup failed: {e}")

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return {
            'model_type': self.model_type,
            'model_name': self.model_name,
            'model_info': self.model_info,
            'is_loaded': self._is_loaded,
            'confidence_threshold': self.confidence_threshold,
            'zoo_path': self.zoo_path
        }

    def is_real_model(self) -> bool:
        """Check if this is a real model (not dummy)."""
        return self.model_type != 'dummy'

    def set_confidence_threshold(self, threshold: float) -> None:
        """Set confidence threshold for detections."""
        self.confidence_threshold = max(0.0, min(1.0, threshold))
        logger.debug(f"Confidence threshold set to {self.confidence_threshold}")


def create_face_detection_model(
    model_type: str = 'auto',
    model_name: Optional[str] = None,
    zoo_path: Optional[str] = None,
    confidence_threshold: float = 0.5
) -> FaceDetectionModelAdapter:
    """
    Factory function to create a face detection model.
    
    Args:
        model_type: Type of model to create
        model_name: Specific model name
        zoo_path: Path to model zoo
        confidence_threshold: Confidence threshold
        
    Returns:
        Configured FaceDetectionModelAdapter
    """
    return FaceDetectionModelAdapter(
        model_type=model_type,
        model_name=model_name,
        zoo_path=zoo_path,
        confidence_threshold=confidence_threshold
    )
