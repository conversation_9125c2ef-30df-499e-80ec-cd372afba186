#!/usr/bin/env python3
"""
Test Ctrl+C handling in Ray VPF demo.
"""

import subprocess
import time
import signal
import sys

def test_ctrl_c_handling():
    """Test that Ctrl+C properly stops the demo."""
    print("🧪 Testing Ctrl+C handling...")
    
    # Start the demo in background
    cmd = [
        "bash", "-c", 
        "cd /home/<USER>/jk/dev/hailotest && source cam-env/bin/activate && python ray_vpf_demo.py --webcam --console"
    ]
    
    print("Starting demo...")
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        preexec_fn=lambda: signal.signal(signal.SIGINT, signal.SIG_DFL)
    )
    
    # Let it run for a few seconds
    print("Letting demo run for 5 seconds...")
    time.sleep(5)
    
    # Send Ctrl+C (SIGINT)
    print("Sending Ctrl+C signal...")
    process.send_signal(signal.SIGINT)
    
    # Wait for graceful shutdown
    print("Waiting for graceful shutdown...")
    try:
        stdout, _ = process.communicate(timeout=10)
        print("✅ Demo stopped gracefully!")
        
        # Check for graceful shutdown indicators
        if "🛑 Shutdown requested" in stdout:
            print("✅ Signal handler triggered correctly")
        if "Pipeline stopped successfully" in stdout:
            print("✅ Pipeline stopped gracefully")
        if "Camera stopped" in stdout:
            print("✅ Camera stopped properly")
            
        print(f"Exit code: {process.returncode}")
        
    except subprocess.TimeoutExpired:
        print("❌ Demo did not stop within timeout, force killing...")
        process.kill()
        process.wait()
        return False
    
    return True

def main():
    print("🚀 RAY VPF CTRL+C TEST")
    print("="*50)
    
    success = test_ctrl_c_handling()
    
    print("\n" + "="*50)
    if success:
        print("✅ CTRL+C HANDLING TEST PASSED!")
        print("The demo now responds properly to Ctrl+C signals.")
    else:
        print("❌ CTRL+C HANDLING TEST FAILED!")
        print("The demo may still have issues with signal handling.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
