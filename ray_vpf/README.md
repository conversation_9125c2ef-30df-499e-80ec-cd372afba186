# Ray VPF (Video Processing Framework)

A simple, powerful, and reusable video processing framework designed for easy integration and rapid development of video analytics applications.

## Key Features

- **KISS Principle**: Keep It Simple, Stupid - easy to understand and use
- **Object-Oriented Design**: Clean abstractions following SOLID principles
- **Production Ready**: Robust error handling and resource management
- **Supervision Integration**: Advanced tracking and visualization capabilities
- **Multiple Sources**: Support for video files, webcams, and Raspberry Pi camera
- **Extensible**: Easy to add new models, processors, and sinks

## Quick Start

### Basic Usage

```python
from ray_vpf import VideoFileSource, DummyModel, BaseFrameProcessor, BufferedPipeline

# Create components
source = VideoFileSource("input.mp4")
model = DummyModel()
processor = BaseFrameProcessor()

# Create and run pipeline
pipeline = BufferedPipeline(source, model, processor)
pipeline.start()

# Process frames...
```

### Custom Processor

```python
from ray_vpf import BaseFrameProcessor

class MyProcessor(BaseFrameProcessor):
    def process(self, pkt):
        # Parse detections
        detections = self.parse_detections(pkt.model_out, pkt.frame_rgb.shape[:2])
        
        # Custom processing logic here
        filtered_detections = [d for d in detections if d.score > 0.5]
        
        # Draw results
        return self.draw_boxes(pkt.frame_rgb, filtered_detections)
```

### With Supervision Integration

```python
from ray_vpf import EnhancedFrameProcessor, create_hailo_showcase_processor

# Easy way - use factory function
processor = create_hailo_showcase_processor(
    conf_threshold=0.5,
    enable_tracking=True,
    tracking_sensitivity='medium'
)

# Or create manually
processor = EnhancedFrameProcessor(
    conf_threshold=0.5,
    enable_supervision=True,
    enable_tracking=True
)
```

## Architecture

The framework is organized into logical modules:

- **core**: Abstract base classes and data structures
- **sources**: Video input sources (files, cameras)
- **models**: Inference model implementations
- **processors**: Frame processing and supervision integration
- **sinks**: Output destinations (files, streams)
- **pipeline**: Multi-threaded processing pipeline
- **utils**: Utility functions and helpers

## Components

### Sources
- `VideoFileSource`: Production-ready video file reader
- `Picamera2Source`: Raspberry Pi camera support
- `WebcamSource`: USB webcam support

### Models
- `DegirumRetinaFaceModel`: Hailo/Degirum model adapter
- `DummyModel`: Synthetic detections for testing

### Processors
- `BaseFrameProcessor`: Basic detection processing
- `EnhancedFrameProcessor`: With supervision integration
- `SupervisionConverter`: Format conversion utilities
- `SupervisionTracker`: Object tracking wrapper

### Sinks
- `OpenCVFileSink`: Video file output with proper FPS

### Pipeline
- `BufferedPipeline`: Multi-threaded processing pipeline

## Installation

1. Copy the `ray_vpf` folder to your project
2. Install dependencies:
   ```bash
   pip install opencv-python numpy
   
   # Optional: for advanced features
   pip install supervision
   
   # For Raspberry Pi camera
   pip install picamera2
   
   # For Hailo/Degirum models
   pip install degirum
   ```

## Demo

Run the included demo to see the framework in action:

```bash
# Process video file
python ray_vpf_demo.py --video-file input.mp4

# Use webcam
python ray_vpf_demo.py --webcam

# Save output
python ray_vpf_demo.py --video-file input.mp4 --output demo_output.mp4

# Headless mode
python ray_vpf_demo.py --video-file input.mp4 --headless
```

## Design Principles

1. **Simplicity**: Easy to understand and use
2. **Modularity**: Clean separation of concerns
3. **Extensibility**: Easy to add new components
4. **Robustness**: Proper error handling and resource management
5. **Performance**: Efficient multi-threaded processing
6. **Compatibility**: Works with existing code and libraries

## License

This framework is designed to be simple and reusable. Feel free to adapt it for your needs.
