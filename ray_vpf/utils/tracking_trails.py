"""
Tracking trails visualization utilities for Ray VPF.
"""

import cv2
import numpy as np
import logging
from collections import defaultdict, deque
from typing import List, Tuple, Dict, Optional, Generator

# Optional supervision import
try:
    import supervision as sv
    SUPERVISION_AVAILABLE = True
except ImportError:
    sv = None
    SUPERVISION_AVAILABLE = False

logger = logging.getLogger(__name__)


class TrackingTrailsVisualizer:
    """
    Professional tracking trails visualizer with advanced features.
    
    This class provides sophisticated visualization of object tracking trails
    with features like fading effects, color management, and performance optimization.
    """
    
    def __init__(
        self, 
        max_trail_length: int = 30, 
        trail_thickness: int = 2,
        fade_effect: bool = True,
        color_persistence: bool = True
    ):
        """
        Initialize the tracking trails visualizer.
        
        Args:
            max_trail_length: Maximum number of points in each trail
            trail_thickness: Base thickness of trail lines
            fade_effect: Whether to apply fading effect to trails
            color_persistence: Whether to maintain consistent colors for tracks
        """
        self.max_trail_length = max_trail_length
        self.trail_thickness = trail_thickness
        self.fade_effect = fade_effect
        self.color_persistence = color_persistence
        
        # Trail storage: track_id -> deque of (x, y) points
        self.trails = defaultdict(lambda: deque(maxlen=max_trail_length))
        
        # Color management
        self.colors = {}
        self._color_generator = self._generate_colors()
        
        # Performance tracking
        self._update_count = 0
        self._cleanup_interval = 100  # Cleanup every N updates
        
        logger.debug(f"TrackingTrailsVisualizer initialized: max_length={max_trail_length}, "
                    f"thickness={trail_thickness}, fade={fade_effect}")
    
    def _generate_colors(self) -> Generator[Tuple[int, int, int], None, None]:
        """Generate distinct colors for different tracks."""
        # Predefined distinct colors (BGR format for OpenCV)
        base_colors = [
            (255, 0, 0),    # Red
            (0, 255, 0),    # Green
            (0, 0, 255),    # Blue
            (255, 255, 0),  # Cyan
            (255, 0, 255),  # Magenta
            (0, 255, 255),  # Yellow
            (255, 128, 0),  # Orange
            (128, 255, 0),  # Lime
            (255, 0, 128),  # Pink
            (128, 0, 255),  # Purple
            (0, 128, 255),  # Light Blue
            (0, 255, 128),  # Spring Green
            (128, 128, 255), # Light Purple
            (255, 128, 128), # Light Red
            (128, 255, 128), # Light Green
            (128, 128, 128), # Gray
        ]
        
        # Generate variations of base colors
        while True:
            for base_color in base_colors:
                yield base_color
                # Generate lighter and darker variations
                for factor in [0.7, 1.3]:
                    varied_color = tuple(
                        min(255, max(0, int(c * factor))) for c in base_color
                    )
                    yield varied_color
    
    def update_trails(self, detections: "sv.Detections") -> None:
        """
        Update tracking trails with new detections.
        
        Args:
            detections: Supervision detections with tracking information
        """
        if not SUPERVISION_AVAILABLE:
            logger.warning("Supervision not available for trail updates")
            return
            
        if detections is None or len(detections) == 0:
            return
            
        if not hasattr(detections, 'tracker_id') or detections.tracker_id is None:
            logger.debug("No tracking IDs available in detections")
            return
        
        self._update_count += 1
        
        # Update trails for each tracked detection
        for i, track_id in enumerate(detections.tracker_id):
            if track_id is not None and i < len(detections.xyxy):
                # Get center point of bounding box
                x1, y1, x2, y2 = detections.xyxy[i]
                center = (int((x1 + x2) / 2), int((y1 + y2) / 2))
                
                # Add to trail
                self.trails[track_id].append(center)
                
                # Assign color if new track
                if self.color_persistence and track_id not in self.colors:
                    self.colors[track_id] = next(self._color_generator)
        
        # Periodic cleanup
        if self._update_count % self._cleanup_interval == 0:
            self._periodic_cleanup()
    
    def draw_trails(self, frame: np.ndarray) -> np.ndarray:
        """
        Draw tracking trails on the frame.
        
        Args:
            frame: Input frame to draw trails on
            
        Returns:
            Frame with trails drawn
        """
        if not self.trails:
            return frame
        
        # Create overlay for trails
        overlay = frame.copy()
        
        for track_id, trail in self.trails.items():
            if len(trail) < 2:
                continue
            
            # Get color for this track
            if self.color_persistence and track_id in self.colors:
                color = self.colors[track_id]
            else:
                # Use a default color or generate one
                color = (255, 255, 255)  # White default
            
            points = list(trail)
            
            if self.fade_effect:
                # Draw trail with fading effect
                self._draw_fading_trail(overlay, points, color)
            else:
                # Draw simple trail
                self._draw_simple_trail(overlay, points, color)
        
        # Blend with original frame for transparency effect
        return cv2.addWeighted(frame, 0.7, overlay, 0.3, 0)
    
    def _draw_fading_trail(self, overlay: np.ndarray, points: List[Tuple[int, int]], color: Tuple[int, int, int]) -> None:
        """Draw trail with fading effect."""
        num_points = len(points)
        
        for i in range(1, num_points):
            # Calculate alpha (opacity) based on position in trail
            alpha = i / num_points  # Fade from 0 to 1
            thickness = max(1, int(self.trail_thickness * alpha))
            
            # Adjust color intensity based on alpha
            faded_color = tuple(int(c * alpha) for c in color)
            
            try:
                cv2.line(overlay, points[i-1], points[i], faded_color, thickness)
            except Exception as e:
                logger.debug(f"Error drawing trail line: {e}")
                continue
    
    def _draw_simple_trail(self, overlay: np.ndarray, points: List[Tuple[int, int]], color: Tuple[int, int, int]) -> None:
        """Draw simple trail without fading."""
        for i in range(1, len(points)):
            try:
                cv2.line(overlay, points[i-1], points[i], color, self.trail_thickness)
            except Exception as e:
                logger.debug(f"Error drawing simple trail line: {e}")
                continue
    
    def cleanup_old_trails(self, active_track_ids: List[int]) -> None:
        """
        Remove trails for inactive tracks.
        
        Args:
            active_track_ids: List of currently active tracking IDs
        """
        if not active_track_ids:
            return
            
        active_set = set(active_track_ids)
        inactive_tracks = set(self.trails.keys()) - active_set
        
        for track_id in inactive_tracks:
            if track_id in self.trails:
                del self.trails[track_id]
            if self.color_persistence and track_id in self.colors:
                del self.colors[track_id]
        
        if inactive_tracks:
            logger.debug(f"Cleaned up {len(inactive_tracks)} inactive trails")
    
    def _periodic_cleanup(self) -> None:
        """Perform periodic cleanup of empty trails."""
        empty_trails = [track_id for track_id, trail in self.trails.items() if len(trail) == 0]
        
        for track_id in empty_trails:
            del self.trails[track_id]
            if self.color_persistence and track_id in self.colors:
                del self.colors[track_id]
        
        if empty_trails:
            logger.debug(f"Periodic cleanup removed {len(empty_trails)} empty trails")
    
    def clear_all_trails(self) -> None:
        """Clear all trails and reset the visualizer."""
        self.trails.clear()
        if self.color_persistence:
            self.colors.clear()
        logger.debug("All trails cleared")
    
    def get_trail_count(self) -> int:
        """Get the number of active trails."""
        return len(self.trails)
    
    def get_trail_info(self) -> Dict[str, any]:
        """Get information about current trails."""
        return {
            'active_trails': len(self.trails),
            'total_points': sum(len(trail) for trail in self.trails.values()),
            'avg_trail_length': sum(len(trail) for trail in self.trails.values()) / max(len(self.trails), 1),
            'max_trail_length': max((len(trail) for trail in self.trails.values()), default=0)
        }
    
    def set_trail_length(self, new_length: int) -> None:
        """
        Dynamically change the maximum trail length.
        
        Args:
            new_length: New maximum trail length
        """
        if new_length <= 0:
            logger.warning("Trail length must be positive")
            return
            
        self.max_trail_length = new_length
        
        # Update existing trails
        for track_id in self.trails:
            # Create new deque with new max length
            old_trail = list(self.trails[track_id])
            self.trails[track_id] = deque(old_trail[-new_length:], maxlen=new_length)
        
        logger.debug(f"Trail length updated to {new_length}")
    
    def set_trail_thickness(self, new_thickness: int) -> None:
        """
        Change the trail thickness.
        
        Args:
            new_thickness: New trail thickness
        """
        if new_thickness <= 0:
            logger.warning("Trail thickness must be positive")
            return
            
        self.trail_thickness = new_thickness
        logger.debug(f"Trail thickness updated to {new_thickness}")


class TrailsConfiguration:
    """Configuration class for tracking trails visualization."""
    
    def __init__(
        self,
        max_trail_length: int = 30,
        trail_thickness: int = 2,
        fade_effect: bool = True,
        color_persistence: bool = True,
        cleanup_interval: int = 100
    ):
        self.max_trail_length = max_trail_length
        self.trail_thickness = trail_thickness
        self.fade_effect = fade_effect
        self.color_persistence = color_persistence
        self.cleanup_interval = cleanup_interval
    
    def to_dict(self) -> Dict[str, any]:
        """Convert configuration to dictionary."""
        return {
            'max_trail_length': self.max_trail_length,
            'trail_thickness': self.trail_thickness,
            'fade_effect': self.fade_effect,
            'color_persistence': self.color_persistence,
            'cleanup_interval': self.cleanup_interval
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, any]) -> 'TrailsConfiguration':
        """Create configuration from dictionary."""
        return cls(**config_dict)
