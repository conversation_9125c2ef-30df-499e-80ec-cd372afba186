"""
Buffered pipeline implementation for Ray VPF.
"""

import time
import queue
import threading
import logging
from typing import List, Optional

import numpy as np

from ..core import FrameSource, InferenceModel, FrameProcessor, FrameSink, FramePacket

logger = logging.getLogger(__name__)


class BufferedPipeline:
    """Capture -> Infer -> Process -> Sink pipeline with bounded queues.

    Attributes exposed for compatibility with existing scripts: preview_q (frames for display)
    """

    def __init__(
        self,
        source: FrameSource,
        model: InferenceModel,
        processor: FrameProcessor,
        sink: Optional[FrameSink] = None,
        raw_sink: Optional[FrameSink] = None,
        input_q_size: int = 8,
        proc_q_size: int = 8,
        save_q_size: int = 128,
        raw_save_q_size: int = 128,
        preview_q_size: int = 1,
        infer_workers: int = 1,
    ) -> None:
        self.source = source
        self.model = model
        self.processor = processor
        self.sink = sink
        self.raw_sink = raw_sink

        self.input_q: queue.Queue = queue.Queue(maxsize=max(1, int(input_q_size)))
        self.proc_q: queue.Queue = queue.Queue(maxsize=max(1, int(proc_q_size)))
        self.save_q: queue.Queue = queue.Queue(maxsize=max(1, int(save_q_size)))
        self.raw_save_q: queue.Queue = queue.Queue(maxsize=max(1, int(raw_save_q_size)))
        self.preview_q: queue.Queue = queue.Queue(maxsize=max(1, int(preview_q_size)))

        self._threads: List[threading.Thread] = []
        self._stop_ev = threading.Event()
        self._frame_idx = 0
        self._infer_workers = max(1, int(infer_workers))

        # Counters for monitoring progress
        self._counter_lock = threading.Lock()
        self._captured_count = 0
        self._inferred_count = 0
        self._processed_count = 0
        self._sink_handed_count = 0
        self._monitor_thread: Optional[threading.Thread] = None

    # ---------------- lifecycle ----------------
    def start(self) -> None:
        """Start the pipeline."""
        logger.info("Starting pipeline")
        self.source.start()
        try:
            # attempt warmup to initialize weights/hardware
            dummy = self.source.read()
            if dummy is not None:
                self.model.warmup(dummy)
                # push dummy back to source by storing it for first read loop? We can't easily push back,
                # so we continue normally - this read consumes one frame from source. It's acceptable.
        except Exception:
            pass

        # start sink
        if self.sink is not None:
            # estimate size from first frame if possible
            self.sink.start()

        # start raw sink
        if self.raw_sink is not None:
            self.raw_sink.start()

        # capture thread
        t_cap = threading.Thread(target=self._capture_loop, daemon=True)
        t_cap.start()
        self._threads.append(t_cap)

        # inference threads
        for _ in range(self._infer_workers):
            t_inf = threading.Thread(target=self._inference_loop, daemon=True)
            t_inf.start()
            self._threads.append(t_inf)

        # processing thread
        t_proc = threading.Thread(target=self._processing_loop, daemon=True)
        t_proc.start()
        self._threads.append(t_proc)

        # sink thread
        if self.sink is not None:
            t_sink = threading.Thread(target=self._sink_loop, daemon=True)
            t_sink.start()
            self._threads.append(t_sink)

        # raw sink thread
        if self.raw_sink is not None:
            t_raw_sink = threading.Thread(target=self._raw_sink_loop, daemon=True)
            t_raw_sink.start()
            self._threads.append(t_raw_sink)

        # monitoring thread (logs counts & queue sizes periodically)
        try:
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            self._threads.append(self._monitor_thread)
        except Exception:
            pass

    def stop_capture(self) -> None:
        """Stop capture only."""
        logger.info("Stopping capture (pipeline)")
        self._stop_ev.set()
        try:
            self.source.stop()
        except Exception:
            pass

    def stop_all(self) -> None:
        """Stop all pipeline threads."""
        logger.info("Stopping all pipeline threads")
        self._stop_ev.set()
        try:
            self.source.stop()
        except Exception:
            pass
        try:
            if self.sink is not None:
                self.sink.stop()
        except Exception:
            pass
        try:
            if self.raw_sink is not None:
                self.raw_sink.stop()
        except Exception:
            pass

    def join(self, timeout: Optional[float] = None) -> None:
        """Wait for threads to finish."""
        start = time.time()
        for t in self._threads:
            remaining = None
            if timeout is not None:
                elapsed = time.time() - start
                remaining = max(0.0, timeout - elapsed)
            t.join(timeout=remaining)

    def _monitor_loop(self):
        """Background monitor that logs pipeline progress periodically."""
        interval = 5.0
        try:
            while not self._stop_ev.is_set():
                time.sleep(interval)
                try:
                    with self._counter_lock:
                        cap = int(self._captured_count)
                        inf = int(self._inferred_count)
                        proc = int(self._processed_count)
                        handed = int(self._sink_handed_count)
                except Exception:
                    cap = inf = proc = handed = 0
                raw_q_size = self.raw_save_q.qsize() if self.raw_sink is not None else 0
                logger.info(
                    "Pipeline progress: captured=%d inferred=%d processed=%d sink_handed=%d | queues: input=%d proc=%d save=%d raw_save=%d preview=%d",
                    cap, inf, proc, handed, self.input_q.qsize(), self.proc_q.qsize(), self.save_q.qsize(), raw_q_size, self.preview_q.qsize(),
                )
            logger.info("Monitor thread exiting")
        except Exception as e:
            logger.exception("Monitor loop error: %s", e)

    # ---------------- core loops ----------------
    def _capture_loop(self) -> None:
        """Capture frames from source."""
        logger.info("Capture loop started")
        while not self._stop_ev.is_set():
            try:
                frame_rgb = self.source.read()
            except Exception as e:
                logger.error("Source read error: %s", e)
                break
            if frame_rgb is None:
                # EOF or no frame - break the loop
                logger.info("No more frames from source")
                self._stop_ev.set()
                break

            # Store raw frame if raw sink is enabled
            if self.raw_sink is not None:
                try:
                    self.raw_save_q.put_nowait(frame_rgb.copy())
                except queue.Full:
                    # Drop oldest raw frame if queue is full
                    try:
                        _ = self.raw_save_q.get_nowait()
                        self.raw_save_q.put_nowait(frame_rgb.copy())
                    except Exception:
                        pass

            # push to input queue; if full drop oldest to keep low-latency preview
            try:
                # increment captured counter
                try:
                    with self._counter_lock:
                        self._captured_count += 1
                except Exception:
                    pass
                self.input_q.put_nowait((self._frame_idx, frame_rgb, time.time()))
            except queue.Full:
                try:
                    _ = self.input_q.get_nowait()
                    self.input_q.put_nowait((self._frame_idx, frame_rgb, time.time()))
                except Exception:
                    pass

            self._frame_idx += 1

        logger.info("Capture loop finished")

    def _inference_loop(self) -> None:
        """Inference worker loop."""
        logger.info("Inference worker started")
        while not self._stop_ev.is_set() or not self.input_q.empty():
            try:
                idx, frame_rgb, ts = self.input_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                model_out = self.model.infer(frame_rgb)
            except Exception as e:
                logger.exception("Model inference error: %s", e)
                model_out = None

            pkt = FramePacket(frame_rgb=frame_rgb, model_out=model_out, timestamp=ts, frame_idx=idx, meta={})
            # increment inferred counter
            try:
                with self._counter_lock:
                    self._inferred_count += 1
            except Exception:
                pass
            # push to proc queue (drop oldest if full)
            try:
                self.proc_q.put_nowait(pkt)
            except queue.Full:
                try:
                    _ = self.proc_q.get_nowait()
                    self.proc_q.put_nowait(pkt)
                except Exception:
                    pass
            finally:
                try:
                    self.input_q.task_done()
                except Exception:
                    pass

        logger.info("Inference worker finished")

    def _processing_loop(self) -> None:
        """Processing worker loop."""
        logger.info("Processing worker started")
        while not self._stop_ev.is_set() or not self.proc_q.empty():
            try:
                pkt = self.proc_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                processed_rgb = self.processor.process(pkt)
            except Exception as e:
                logger.exception("Processor error: %s", e)
                processed_rgb = pkt.frame_rgb

            # increment processed counter
            try:
                with self._counter_lock:
                    self._processed_count += 1
            except Exception:
                pass

            # Put frame for preview (drop oldest)
            try:
                self.preview_q.put_nowait(processed_rgb)
            except queue.Full:
                try:
                    _ = self.preview_q.get_nowait()
                    self.preview_q.put_nowait(processed_rgb)
                except Exception:
                    pass

            # Put frame to save queue if sink present
            if self.sink is not None:
                try:
                    self.save_q.put_nowait(processed_rgb)
                except queue.Full:
                    # For save we prefer oldest drop policy too - drop oldest.
                    try:
                        _ = self.save_q.get_nowait()
                        self.save_q.put_nowait(processed_rgb)
                    except Exception:
                        pass

            try:
                self.proc_q.task_done()
            except Exception:
                pass

        logger.info("Processing worker finished")

    def _sink_loop(self) -> None:
        """Sink worker loop."""
        logger.info("Sink worker started")
        while not self._stop_ev.is_set() or not self.save_q.empty():
            try:
                frame_rgb = self.save_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                # increment counter for frames handed to sink
                try:
                    with self._counter_lock:
                        self._sink_handed_count += 1
                except Exception:
                    pass
                self.sink.put(frame_rgb)
            except Exception as e:
                logger.exception("Sink put error: %s", e)
            finally:
                try:
                    self.save_q.task_done()
                except Exception:
                    pass
        logger.info("Sink worker finished")

    def _raw_sink_loop(self) -> None:
        """Raw sink worker that saves unprocessed frames."""
        logger.info("Raw sink worker started")
        while not self._stop_ev.is_set() or not self.raw_save_q.empty():
            try:
                frame_rgb = self.raw_save_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                self.raw_sink.put(frame_rgb)
            except Exception as e:
                logger.exception("Raw sink put error: %s", e)
            finally:
                try:
                    self.raw_save_q.task_done()
                except Exception:
                    pass
        logger.info("Raw sink worker finished")
