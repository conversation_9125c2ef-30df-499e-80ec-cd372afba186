#!/usr/bin/env python3
"""
Ray VPF Face Tracking Demo Application

This demo showcases how easy it is to build advanced video analytics applications
using the Ray VPF (Video Processing Framework) with real face detection and tracking.

Features demonstrated:
- Real face detection using multiple model backends
- Advanced object tracking with unique IDs
- Professional visualization with tracking trails
- Real-time performance statistics
- Easy configuration and extensibility
- KISS principle in action

Usage:
    python ray_vpf_demo.py --video-file input.mp4 --face-tracking
    python ray_vpf_demo.py --webcam --face-tracking --show-trails
    python ray_vpf_demo.py --help
"""

import argparse
import logging
import time
import sys
import os
import signal

import cv2

# Import Ray VPF framework
try:
    from ray_vpf import (
        VideoFileSource,
        WebcamSource,
        Picamera2Source,
        DummyModel,
        FaceDetectionModelAdapter,
        create_face_detection_model,
        BaseFrameProcessor,
        AdvancedFaceTrackingProcessor,
        create_face_tracking_processor,
        create_face_tracking_processor_preset,
        BufferedPipeline,
        OpenCVFileSink,
        setup_logging,
        get_info,
        ConsoleDisplay,
        SUPERVISION_AVAILABLE
    )
except ImportError as e:
    print(f"Error importing Ray VPF: {e}")
    print("Make sure the ray_vpf folder is in the current directory")
    sys.exit(1)

# Global flag for graceful shutdown
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully."""
    global shutdown_requested
    print("\n🛑 Shutdown requested... Please wait for graceful cleanup.")
    shutdown_requested = True


class SimpleDetectionProcessor(BaseFrameProcessor):
    """
    Simple processor that demonstrates basic detection processing.

    This processor shows how easy it is to create custom processing logic
    using the Ray VPF framework.
    """

    def __init__(self, conf_threshold: float = 0.5):
        super().__init__(conf_threshold)
        self.frame_count = 0

    def process(self, pkt):
        """Process a frame packet and return annotated frame."""
        self.frame_count += 1

        # Parse detections using built-in helper
        h, w = pkt.frame_rgb.shape[:2]
        detections = self.parse_detections(pkt.model_out, (h, w))

        # Filter detections by confidence
        filtered_detections = [
            det for det in detections
            if det.score is None or det.score >= self.conf_threshold
        ]

        # Draw boxes using built-in helper
        annotated_frame = self.draw_boxes(
            pkt.frame_rgb,
            filtered_detections,
            label_field=True,
            score_field=True
        )

        # Add frame counter
        cv2.putText(
            annotated_frame,
            f"Frame: {self.frame_count} | Detections: {len(filtered_detections)}",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )

        return annotated_frame


def create_model(args):
    """Create detection model based on arguments."""
    import logging
    logger = logging.getLogger(__name__)

    if args.face_tracking:
        # Create face detection model
        logger.info("Creating face detection model...")

        model = create_face_detection_model(
            model_type='auto',
            model_name=args.model_name,
            zoo_path=args.zoo_path,
            confidence_threshold=args.conf
        )

        # Load the model to get info
        try:
            model.load()
            model_info = model.get_model_info()
            return model, f"Face Detection: {model_info['model_info']}"
        except Exception as e:
            logger.warning(f"Face detection model failed: {e}, falling back to dummy")
            return DummyModel(), "Dummy Model (face detection failed)"
    else:
        # Use dummy model for basic demo
        return DummyModel(), "Dummy Model (generates synthetic detections)"


def create_processor(args):
    """Create processor based on arguments."""
    import logging
    logger = logging.getLogger(__name__)

    if args.face_tracking:
        # Create advanced face tracking processor
        logger.info("Creating advanced face tracking processor...")

        if args.preset:
            processor = create_face_tracking_processor_preset(args.preset)
        else:
            processor = create_face_tracking_processor(
                conf_threshold=args.conf,
                tracking_sensitivity=args.tracking_sensitivity,
                show_trails=args.show_trails,
                show_stats=not args.no_stats,
                trail_length=args.trail_length,
                stats_position=args.stats_position
            )

        processor_info = f"AdvancedFaceTrackingProcessor (sensitivity={args.tracking_sensitivity}, trails={args.show_trails})"
        return processor, processor_info
    else:
        # Use simple processor for basic demo
        processor = SimpleDetectionProcessor(conf_threshold=args.conf)
        return processor, f"SimpleDetectionProcessor (conf_threshold={args.conf})"


def create_video_source(args):
    """Create video source based on arguments."""
    import logging
    logger = logging.getLogger(__name__)

    if args.video_file:
        if not os.path.exists(args.video_file):
            raise FileNotFoundError(f"Video file not found: {args.video_file}")

        source = VideoFileSource(
            path=args.video_file,
            realtime=args.realtime,
            loop=args.loop
        )
        source_info = f"Video file: {args.video_file}"

    elif args.webcam:
        # Auto-detect best camera source for the platform
        source, source_info = _create_camera_source(args)
    else:
        raise ValueError("Please specify either --video-file or --webcam")

    return source, source_info


def _create_camera_source(args):
    """Create the best camera source for the current platform."""
    import logging
    logger = logging.getLogger(__name__)

    # Try Picamera2 first on Raspberry Pi
    if _is_raspberry_pi():
        try:
            logger.info("Detected Raspberry Pi, trying Picamera2...")
            source = Picamera2Source(size=(640, 480), format="RGB888")
            source_info = "Raspberry Pi Camera (Picamera2)"
            return source, source_info
        except Exception as e:
            logger.warning(f"Picamera2 failed: {e}, falling back to USB webcam")

    # Fallback to USB webcam
    try:
        logger.info("Trying USB webcam...")
        source = WebcamSource(camera_index=0, auto_detect=True)
        source_info = "USB Webcam (OpenCV)"
        return source, source_info
    except Exception as e:
        logger.error(f"USB webcam failed: {e}")
        raise RuntimeError(f"No camera sources available. Picamera2 and USB webcam both failed.")


def _is_raspberry_pi():
    """Check if running on Raspberry Pi."""
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo = f.read()
        return 'Raspberry Pi' in cpuinfo or 'BCM' in cpuinfo
    except:
        return False


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Ray VPF Face Tracking Demo - Advanced Video Processing Framework Demo",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Basic demo with dummy detections
    python ray_vpf_demo.py --video-file input.mp4

    # Face tracking demo
    python ray_vpf_demo.py --video-file input.mp4 --face-tracking --show-trails

    # Webcam face tracking
    python ray_vpf_demo.py --webcam --face-tracking --show-trails

    # High quality face tracking
    python ray_vpf_demo.py --video-file input.mp4 --face-tracking --preset quality

    # Save output with face tracking
    python ray_vpf_demo.py --video-file input.mp4 --face-tracking --output face_tracking_output.mp4

    # Console mode for headless systems
    python ray_vpf_demo.py --video-file input.mp4 --face-tracking --console
        """
    )
    
    # Input source
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "--video-file", type=str,
        help="Path to input video file"
    )
    input_group.add_argument(
        "--webcam", action="store_true",
        help="Use webcam as input"
    )
    
    # Processing options
    parser.add_argument(
        "--conf", type=float, default=0.5,
        help="Confidence threshold for detections (default: 0.5)"
    )

    # Face tracking options
    parser.add_argument(
        "--face-tracking", action="store_true",
        help="Enable advanced face detection and tracking"
    )
    parser.add_argument(
        "--model-name", type=str,
        help="Specific face detection model name (for Hailo/Degirum)"
    )
    parser.add_argument(
        "--zoo-path", type=str, default="~/degirum-zoo",
        help="Path to model zoo (default: ~/degirum-zoo)"
    )
    parser.add_argument(
        "--tracking-sensitivity", type=str, default="medium",
        choices=["low", "medium", "high"],
        help="Tracking sensitivity level (default: medium)"
    )
    parser.add_argument(
        "--show-trails", action="store_true",
        help="Show tracking trails"
    )
    parser.add_argument(
        "--trail-length", type=int, default=30,
        help="Maximum trail length (default: 30)"
    )
    parser.add_argument(
        "--no-stats", action="store_true",
        help="Disable statistics overlay"
    )
    parser.add_argument(
        "--stats-position", type=str, default="top-left",
        choices=["top-left", "top-right", "bottom-left", "bottom-right"],
        help="Position for statistics overlay (default: top-left)"
    )
    parser.add_argument(
        "--preset", type=str,
        choices=["performance", "balanced", "quality", "demo"],
        help="Use predefined configuration preset"
    )
    
    # Output options
    parser.add_argument(
        "--output", type=str,
        help="Path to save output video"
    )
    parser.add_argument(
        "--fps", type=float, default=30.0,
        help="Output video FPS (default: 30.0)"
    )
    
    # Video options
    parser.add_argument(
        "--realtime", action="store_true",
        help="Play video in real-time (for video files)"
    )
    parser.add_argument(
        "--loop", action="store_true",
        help="Loop video playback (for video files)"
    )
    
    # Display options
    parser.add_argument(
        "--headless", action="store_true",
        help="Run without display window"
    )
    parser.add_argument(
        "--console", action="store_true",
        help="Use console mode with text-based statistics"
    )
    
    # Debug options
    parser.add_argument(
        "--debug", action="store_true",
        help="Enable debug logging"
    )
    
    return parser.parse_args()


def main():
    """Main function."""
    global shutdown_requested

    # Parse arguments
    args = parse_arguments()

    # Setup signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Setup logging
    log_level = "DEBUG" if args.debug else "INFO"
    setup_logging(log_level)
    logger = logging.getLogger(__name__)
    
    # Print framework info
    info = get_info()
    print("\n" + "="*60)
    print(f"RAY VPF FACE TRACKING DEMO - {info['name']} v{info['version']}")
    print("="*60)
    print(f"Description: {info['description']}")
    print(f"Supervision Available: {info['supervision_available']}")
    print(f"Face Tracking Mode: {'Enabled' if args.face_tracking else 'Disabled'}")
    if args.face_tracking and not info['supervision_available']:
        print("WARNING: Supervision not available - tracking features will be limited")
    print("="*60)
    
    try:
        # Create video source
        logger.info("Creating video source...")
        source, source_info = create_video_source(args)
        print(f"Video Source: {source_info}")
        
        # Create model
        model, model_info = create_model(args)
        print(f"Model: {model_info}")

        # Create processor
        processor, processor_info = create_processor(args)
        print(f"Processor: {processor_info}")
        
        # Create output sink if specified
        sink = None
        if args.output:
            sink = OpenCVFileSink(args.output, fps=args.fps)
            print(f"Output: {args.output} @ {args.fps} FPS")
        
        print("="*60)
        
        # Create pipeline
        logger.info("Creating pipeline...")
        pipeline = BufferedPipeline(
            source=source,
            model=model,
            processor=processor,
            sink=sink
        )
        
        # Start pipeline
        logger.info("Starting pipeline...")
        pipeline.start()
        
        # Setup display
        console_display = None
        window_name = None

        if args.console:
            # Console mode with text-based statistics
            console_display = ConsoleDisplay(update_interval=2.0)
            print("Console mode: Statistics will be displayed in terminal")
        elif not args.headless:
            # GUI mode with OpenCV window
            try:
                window_name = "Ray VPF Face Tracking Demo - Press 'q' to quit"
                cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                cv2.resizeWindow(window_name, 1280, 720)
                print("GUI mode: Press 'q' to quit, 'r' to reset stats, 's' for screenshot")
            except Exception as e:
                logger.warning(f"Failed to create GUI window: {e}")
                logger.info("Falling back to console mode")
                console_display = ConsoleDisplay(update_interval=2.0)
                window_name = None
        else:
            print("Headless mode: Press Ctrl+C to stop")
        
        print("="*60)
        
        # Main processing loop
        frame_count = 0
        start_time = time.time()
        
        try:
            while True:
                # Check for shutdown request
                if shutdown_requested:
                    logger.info("Shutdown requested, stopping pipeline...")
                    break

                # Get processed frame
                try:
                    processed_frame = pipeline.preview_q.get(timeout=1.0)
                    frame_count += 1
                except:
                    # Check if pipeline finished
                    if hasattr(pipeline, '_stop_ev') and pipeline._stop_ev.is_set():
                        logger.info("Pipeline finished")
                        break
                    # Check for shutdown again during timeout
                    if shutdown_requested:
                        logger.info("Shutdown requested during timeout")
                        break
                    continue
                
                if processed_frame is None:
                    logger.info("End of video stream")
                    break
                
                # Handle different display modes
                if window_name is not None:
                    # GUI mode with OpenCV window
                    try:
                        # Convert RGB to BGR for OpenCV display
                        display_frame = cv2.cvtColor(processed_frame, cv2.COLOR_RGB2BGR)
                        cv2.imshow(window_name, display_frame)

                        # Handle keyboard input
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q') or key == 27:  # 'q' or ESC
                            logger.info("Quit requested by user")
                            shutdown_requested = True
                            break
                        elif key == ord('r') and hasattr(processor, 'reset_statistics'):
                            processor.reset_statistics()
                            logger.info("Statistics reset")
                        elif key == ord('s'):
                            # Save screenshot
                            timestamp = time.strftime("%Y%m%d_%H%M%S")
                            screenshot_path = f"face_tracking_screenshot_{timestamp}.jpg"
                            cv2.imwrite(screenshot_path, display_frame)
                            logger.info(f"Screenshot saved: {screenshot_path}")
                        elif key == ord('t') and hasattr(processor, 'toggle_trails'):
                            processor.toggle_trails()
                        elif key == ord('i') and hasattr(processor, 'toggle_stats'):
                            processor.toggle_stats()
                    except Exception as e:
                        logger.warning(f"Display error: {e}")
                        # Fall back to console mode
                        if console_display is None:
                            console_display = ConsoleDisplay(update_interval=2.0)
                        window_name = None

                elif console_display is not None:
                    # Console mode with text-based statistics
                    if hasattr(processor, 'get_statistics'):
                        stats = processor.get_statistics()
                        console_display.update(stats, frame_count)
                    else:
                        # Basic stats for simple processor
                        elapsed = time.time() - start_time
                        avg_fps = frame_count / elapsed if elapsed > 0 else 0
                        basic_stats = {
                            'runtime': elapsed,
                            'total_frames': frame_count,
                            'avg_fps': avg_fps,
                            'current_fps': avg_fps
                        }
                        console_display.update(basic_stats, frame_count)
                
                # Print progress every 30 frames
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    fps = frame_count / elapsed if elapsed > 0 else 0
                    logger.info(f"Processed {frame_count} frames, FPS: {fps:.1f}")
        
        except KeyboardInterrupt:
            logger.info("Interrupted by user")
        
        finally:
            # Cleanup
            logger.info("Stopping pipeline...")
            try:
                pipeline.stop_all()
                logger.info("Waiting for pipeline threads to finish...")
                pipeline.join(timeout=3.0)
                logger.info("Pipeline stopped successfully")
            except Exception as e:
                logger.warning(f"Error stopping pipeline: {e}")
                logger.info("Forcing pipeline shutdown...")
                # Force stop if graceful shutdown fails
                try:
                    if hasattr(pipeline, '_stop_ev'):
                        pipeline._stop_ev.set()
                    if hasattr(pipeline, 'source') and hasattr(pipeline.source, 'stop'):
                        pipeline.source.stop()
                except Exception as e2:
                    logger.debug(f"Force stop error: {e2}")
            
            if window_name is not None:
                cv2.destroyAllWindows()
            
            # Final stats
            elapsed = time.time() - start_time
            avg_fps = frame_count / elapsed if elapsed > 0 else 0

            # Get detailed stats if available
            if hasattr(processor, 'get_statistics'):
                final_stats = processor.get_statistics()
                if console_display is not None:
                    console_display.final_report(final_stats, args.output)
                else:
                    print("\n" + "="*60)
                    print("FACE TRACKING DEMO COMPLETED")
                    print("="*60)
                    print(f"Total Runtime: {final_stats.get('runtime', elapsed):.1f} seconds")
                    print(f"Frames Processed: {final_stats.get('total_frames', frame_count)}")
                    print(f"Average FPS: {final_stats.get('avg_fps', avg_fps):.1f}")
                    if 'frames_with_faces' in final_stats:
                        print(f"Frames with Faces: {final_stats['frames_with_faces']} ({final_stats.get('face_detection_rate', 0):.1%})")
                        print(f"Unique Faces Tracked: {final_stats.get('unique_faces_seen', 0)}")
                        print(f"Max Concurrent Faces: {final_stats.get('max_concurrent_faces', 0)}")
                    if args.output:
                        print(f"Output Video Saved: {args.output}")
                    print("="*60)
            else:
                # Basic stats for simple demo
                print("\n" + "="*60)
                print("DEMO COMPLETED")
                print("="*60)
                print(f"Total Frames: {frame_count}")
                print(f"Total Time: {elapsed:.1f}s")
                print(f"Average FPS: {avg_fps:.1f}")
                if args.output:
                    print(f"Output saved: {args.output}")
                print("="*60)
        
        return 0
    
    except Exception as e:
        logger.error(f"Demo error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
