"""
Advanced frame processors with face tracking and enhanced visualization for Ray VPF.
"""

import cv2
import numpy as np
import logging
from typing import Dict, List, Optional, Any

from ..core import FramePacket
from .supervision_integration import EnhancedFrameProcessor
from ..utils import FaceTrackingStats, TrackingTrailsVisualizer

# Optional supervision import
try:
    import supervision as sv
    SUPERVISION_AVAILABLE = True
except ImportError:
    sv = None
    SUPERVISION_AVAILABLE = False

logger = logging.getLogger(__name__)


class AdvancedFaceTrackingProcessor(EnhancedFrameProcessor):
    """
    Advanced face tracking processor with enhanced visualization and statistics.
    
    This processor extends the EnhancedFrameProcessor to provide:
    - Advanced tracking trails visualization
    - Real-time performance statistics
    - Professional annotations with tracking information
    - Configurable display options
    - Comprehensive error handling and fallback mechanisms
    """
    
    def __init__(
        self,
        conf_threshold: float = 0.5,
        tracking_sensitivity: str = 'medium',
        show_trails: bool = False,
        show_stats: bool = True,
        trail_length: int = 30,
        enable_tracking: bool = True,
        stats_position: str = 'top-left',
        trail_thickness: int = 2,
        fade_effect: bool = True,
        **kwargs
    ):
        """
        Initialize the advanced face tracking processor.
        
        Args:
            conf_threshold: Confidence threshold for detections
            tracking_sensitivity: Tracking sensitivity ('low', 'medium', 'high')
            show_trails: Whether to show tracking trails
            show_stats: Whether to show statistics overlay
            trail_length: Maximum length of tracking trails
            enable_tracking: Whether to enable object tracking
            stats_position: Position for stats overlay ('top-left', 'top-right', 'bottom-left', 'bottom-right')
            trail_thickness: Thickness of trail lines
            fade_effect: Whether to apply fading effect to trails
            **kwargs: Additional arguments for parent class
        """
        # Configure tracking parameters based on sensitivity
        tracking_params = self._get_tracking_params(tracking_sensitivity)

        super().__init__(
            conf_threshold=conf_threshold,
            enable_supervision=True,
            enable_tracking=enable_tracking,
            tracker_config=tracking_params,
            **kwargs
        )
        
        # Visualization components
        self.show_trails = show_trails
        self.show_stats = show_stats
        self.stats_position = stats_position
        
        # Initialize tracking trails visualizer
        self.trails_visualizer = TrackingTrailsVisualizer(
            max_trail_length=trail_length,
            trail_thickness=trail_thickness,
            fade_effect=fade_effect
        )
        
        # Initialize statistics tracker
        self.stats = FaceTrackingStats()
        
        # Performance tracking
        self._frame_count = 0
        self._error_count = 0
        
        logger.info(f"AdvancedFaceTrackingProcessor initialized with tracking_sensitivity='{tracking_sensitivity}', "
                   f"show_trails={show_trails}, show_stats={show_stats}")
    
    def _get_tracking_params(self, sensitivity: str) -> Dict[str, Any]:
        """Get tracking parameters based on sensitivity level."""
        sensitivity_configs = {
            'low': {
                'track_activation_threshold': 0.7,
                'lost_track_buffer': 15,
                'minimum_matching_threshold': 0.9,
                'enable_smoothing': False
            },
            'medium': {
                'track_activation_threshold': 0.5,
                'lost_track_buffer': 30,
                'minimum_matching_threshold': 0.8,
                'enable_smoothing': True
            },
            'high': {
                'track_activation_threshold': 0.3,
                'lost_track_buffer': 50,
                'minimum_matching_threshold': 0.7,
                'enable_smoothing': True
            }
        }
        
        return sensitivity_configs.get(sensitivity, sensitivity_configs['medium'])

    def process(self, pkt: FramePacket) -> np.ndarray:
        """
        Process frame with advanced face tracking and visualization.

        Args:
            pkt: FramePacket containing frame and model output

        Returns:
            Processed frame with professional annotations and tracking
        """
        self._frame_count += 1
        
        try:
            # Debug logging
            logger.debug(f"Processing frame {pkt.frame_idx}, model_out type: {type(pkt.model_out)}")

            # Check if supervision is available
            if not self.enable_supervision:
                logger.warning("Supervision not enabled, falling back to basic processing")
                return self._fallback_processing(pkt)

            # Convert model output to supervision format
            h, w = pkt.frame_rgb.shape[:2]
            sv_detections = self.converter.convert_to_supervision(
                pkt.model_out,
                source_format='hailo',
                frame_shape=(h, w),
                class_names=None
            )

            logger.debug(f"Converted to supervision: {sv_detections is not None}, count: {len(sv_detections) if sv_detections else 0}")

            # Apply tracking if enabled and we have detections
            if self.tracker and self.tracker.is_available() and sv_detections is not None and len(sv_detections) > 0:
                logger.debug("Applying tracking...")
                try:
                    sv_detections = self.tracker.update_with_detections(sv_detections)
                    logger.debug(f"Tracking applied, count: {len(sv_detections) if sv_detections else 0}")
                except Exception as e:
                    logger.error(f"Tracking failed: {e}")
                    self._error_count += 1
                    # Continue without tracking

            # Handle case with no detections
            if sv_detections is None or len(sv_detections) == 0:
                # Update stats for frame with no faces
                self.stats.update_frame(0, [])
                return self._draw_stats_overlay(pkt.frame_rgb.copy(), sv_detections)

            # Extract tracking information
            active_track_ids = []
            if hasattr(sv_detections, 'tracker_id') and sv_detections.tracker_id is not None:
                active_track_ids = [tid for tid in sv_detections.tracker_id if tid is not None]

            # Update statistics
            self.stats.update_frame(len(sv_detections), active_track_ids)

            # Start with original frame
            annotated_frame = pkt.frame_rgb.copy()

            # Draw tracking trails if enabled
            if self.show_trails and active_track_ids:
                self.trails_visualizer.update_trails(sv_detections)
                annotated_frame = self.trails_visualizer.draw_trails(annotated_frame)
                self.trails_visualizer.cleanup_old_trails(active_track_ids)

            # Apply professional supervision annotations
            if self.box_annotator:
                annotated_frame = self.box_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections
                )

            if self.label_annotator:
                labels = self._create_enhanced_labels(sv_detections)
                annotated_frame = self.label_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections,
                    labels=labels
                )

            # Add statistics overlay if enabled
            if self.show_stats:
                annotated_frame = self._draw_stats_overlay(annotated_frame, sv_detections)

            return annotated_frame

        except Exception as e:
            logger.error(f"Error in face tracking processing: {e}")
            self._error_count += 1
            logger.debug(f"Model output type: {type(pkt.model_out)}, frame shape: {pkt.frame_rgb.shape}")
            
            # Fallback to basic processing
            return self._fallback_processing(pkt)

    def _fallback_processing(self, pkt: FramePacket) -> np.ndarray:
        """Fallback processing when advanced features fail."""
        try:
            # Try parent class processing
            return super().process(pkt)
        except Exception as e2:
            logger.error(f"Fallback processing also failed: {e2}")
            # Return original frame as last resort
            frame = pkt.frame_rgb.copy()
            # Add error message
            cv2.putText(frame, "Processing Error - See Logs", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            return frame

    def _create_enhanced_labels(self, sv_detections: "sv.Detections") -> List[str]:
        """Create enhanced labels with tracking IDs and confidence scores."""
        labels = []

        for i in range(len(sv_detections)):
            label_parts = []

            # Add tracking ID if available
            if hasattr(sv_detections, 'tracker_id') and sv_detections.tracker_id is not None:
                if i < len(sv_detections.tracker_id) and sv_detections.tracker_id[i] is not None:
                    label_parts.append(f"Face #{sv_detections.tracker_id[i]}")
                else:
                    label_parts.append("Face")
            else:
                label_parts.append("Face")

            # Add confidence score
            if i < len(sv_detections.confidence):
                confidence = sv_detections.confidence[i]
                label_parts.append(f"{confidence:.2f}")

            labels.append(" | ".join(label_parts))

        return labels

    def _draw_stats_overlay(self, frame: np.ndarray, sv_detections: Optional["sv.Detections"]) -> np.ndarray:
        """Draw performance statistics overlay on the frame."""
        if not self.show_stats:
            return frame

        stats = self.stats.get_summary()

        # Prepare statistics text
        stats_lines = [
            f"FPS: {stats['current_fps']:.1f} (Avg: {stats['avg_fps']:.1f})",
            f"Faces: {len(sv_detections) if sv_detections else 0} | Active Tracks: {stats['active_tracks']}",
            f"Total Frames: {stats['total_frames']} | With Faces: {stats['frames_with_faces']}",
            f"Unique Faces: {stats['unique_faces_seen']} | Max Concurrent: {stats['max_concurrent_faces']}",
            f"Detection Rate: {stats['face_detection_rate']:.1%}",
            f"Runtime: {stats['runtime']:.1f}s"
        ]

        # Add error count if there are errors
        if self._error_count > 0:
            stats_lines.append(f"Errors: {self._error_count}")

        # Calculate position based on stats_position
        h, w = frame.shape[:2]
        stats_width = 400
        stats_height = len(stats_lines) * 25 + 20

        if self.stats_position == 'top-left':
            x, y = 10, 10
        elif self.stats_position == 'top-right':
            x, y = w - stats_width - 10, 10
        elif self.stats_position == 'bottom-left':
            x, y = 10, h - stats_height - 10
        elif self.stats_position == 'bottom-right':
            x, y = w - stats_width - 10, h - stats_height - 10
        else:
            x, y = 10, 10  # Default to top-left

        # Draw semi-transparent background
        overlay = frame.copy()
        cv2.rectangle(overlay, (x, y), (x + stats_width, y + stats_height), (0, 0, 0), -1)
        frame = cv2.addWeighted(frame, 0.7, overlay, 0.3, 0)

        # Draw statistics text
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        color = (255, 255, 255)
        thickness = 1

        for i, line in enumerate(stats_lines):
            text_y = y + 20 + i * 25
            cv2.putText(frame, line, (x + 5, text_y), font, font_scale, color, thickness)

        return frame

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics including error tracking."""
        stats = self.stats.get_summary()
        stats['processing_errors'] = self._error_count
        stats['frames_processed'] = self._frame_count
        stats['error_rate'] = self._error_count / max(self._frame_count, 1)
        return stats

    def reset_statistics(self) -> None:
        """Reset all statistics and trails."""
        self.stats.reset()
        self.trails_visualizer.clear_all_trails()
        self._frame_count = 0
        self._error_count = 0
        logger.info("Statistics and trails reset")

    def configure_trails(self, **kwargs) -> None:
        """Configure trail visualization parameters."""
        if 'max_trail_length' in kwargs:
            self.trails_visualizer.set_trail_length(kwargs['max_trail_length'])
        if 'trail_thickness' in kwargs:
            self.trails_visualizer.set_trail_thickness(kwargs['trail_thickness'])
        
        logger.debug(f"Trail configuration updated: {kwargs}")

    def toggle_trails(self) -> None:
        """Toggle trail visualization on/off."""
        self.show_trails = not self.show_trails
        if not self.show_trails:
            self.trails_visualizer.clear_all_trails()
        logger.info(f"Trails {'enabled' if self.show_trails else 'disabled'}")

    def toggle_stats(self) -> None:
        """Toggle statistics overlay on/off."""
        self.show_stats = not self.show_stats
        logger.info(f"Stats overlay {'enabled' if self.show_stats else 'disabled'}")

    def get_trail_info(self) -> Dict[str, Any]:
        """Get information about current tracking trails."""
        return self.trails_visualizer.get_trail_info()
