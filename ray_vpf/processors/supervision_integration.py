"""
Supervision integration for Ray VPF.

This module provides integration with the supervision library for advanced
object detection and tracking capabilities.
"""

import time
import threading
import logging
from typing import Dict, List, Optional, Tuple, Any
from functools import lru_cache
from weakref import WeakKeyDictionary

import numpy as np

from ..core import FrameProcessor, FramePacket, Detection
from .base_processor import BaseFrameProcessor

# Optional supervision import with graceful fallback
try:
    import supervision as sv
    SUPERVISION_AVAILABLE = True
except ImportError:
    sv = None
    SUPERVISION_AVAILABLE = False

logger = logging.getLogger(__name__)


class SupervisionConverter:
    """
    High-performance conversion layer between various detection formats and Supervision library.

    This class provides a clean, extensible interface for converting detection results
    from different sources (Hailo, YOLO, custom formats) to supervision.Detections format
    and vice versa. It implements intelligent caching and follows SOLID principles.

    Features:
    - Automatic format detection and conversion
    - Intelligent caching to avoid redundant computations
    - Extensible design for adding new detection sources
    - Comprehensive error handling for edge cases
    - Metadata preservation during conversion
    - Thread-safe operations
    """

    def __init__(self, enable_caching: bool = True, cache_size: int = 128):
        """
        Initialize the SupervisionConverter.

        Args:
            enable_caching: Whether to enable intelligent caching for performance
            cache_size: Maximum number of cached conversion results
        """
        self.enable_caching = enable_caching
        self.cache_size = cache_size
        self._conversion_cache = {} if enable_caching else None
        self._cache_hits = 0
        self._cache_misses = 0

        # Thread safety
        self._lock = threading.RLock()

        # Supported detection source formats
        self._supported_formats = {
            'hailo': self._convert_hailo_to_supervision,
            'degirum': self._convert_hailo_to_supervision,  # Degirum uses similar format
            'yolo': self._convert_yolo_to_supervision,
            'generic': self._convert_generic_to_supervision,
            'framework': self._convert_framework_detections_to_supervision
        }

        logger.info(f"SupervisionConverter initialized (caching={'enabled' if enable_caching else 'disabled'}, "
                   f"supervision={'available' if SUPERVISION_AVAILABLE else 'unavailable'})")

    def is_supervision_available(self) -> bool:
        """Check if supervision library is available."""
        return SUPERVISION_AVAILABLE

    def get_cache_stats(self) -> Dict[str, int]:
        """Get caching statistics for performance monitoring."""
        with self._lock:
            return {
                'hits': self._cache_hits,
                'misses': self._cache_misses,
                'hit_rate': self._cache_hits / max(1, self._cache_hits + self._cache_misses),
                'cache_size': len(self._conversion_cache) if self._conversion_cache else 0
            }

    def clear_cache(self) -> None:
        """Clear the conversion cache."""
        with self._lock:
            if self._conversion_cache:
                self._conversion_cache.clear()
            self._cache_hits = 0
            self._cache_misses = 0
            logger.debug("SupervisionConverter cache cleared")

    def convert_to_supervision(
        self,
        detections: Any,
        source_format: str = 'auto',
        frame_shape: Optional[Tuple[int, int]] = None,
        class_names: Optional[List[str]] = None,
        preserve_metadata: bool = True
    ) -> Optional["sv.Detections"]:
        """
        Convert detections from various formats to supervision.Detections.

        Args:
            detections: Detection results in various formats
            source_format: Source format ('hailo', 'yolo', 'generic', 'auto')
            frame_shape: (height, width) for coordinate normalization
            class_names: Optional class names for detections
            preserve_metadata: Whether to preserve additional metadata

        Returns:
            supervision.Detections object or None if conversion fails
        """
        if not SUPERVISION_AVAILABLE:
            logger.warning("Supervision library not available. Install with: pip install supervision")
            return None

        if detections is None:
            return sv.Detections.empty()

        # Auto-detect format if requested
        if source_format == 'auto':
            source_format = self._detect_format(detections)

        # Generate cache key for performance
        cache_key = None
        if self.enable_caching:
            cache_key = self._generate_cache_key(detections, source_format, frame_shape, class_names)

            with self._lock:
                if cache_key in self._conversion_cache:
                    self._cache_hits += 1
                    return self._conversion_cache[cache_key]
                else:
                    self._cache_misses += 1

        try:
            # Perform conversion based on detected/specified format
            converter_func = self._supported_formats.get(source_format, self._convert_generic_to_supervision)
            sv_detections = converter_func(detections, frame_shape, class_names, preserve_metadata)

            # Cache the result if caching is enabled
            if self.enable_caching and cache_key and sv_detections is not None:
                with self._lock:
                    # Implement simple LRU by removing oldest entries
                    if len(self._conversion_cache) >= self.cache_size:
                        # Remove oldest entry (simple FIFO for performance)
                        oldest_key = next(iter(self._conversion_cache))
                        del self._conversion_cache[oldest_key]

                    self._conversion_cache[cache_key] = sv_detections

            return sv_detections

        except Exception as e:
            logger.error(f"Failed to convert detections from {source_format} format: {e}")
            return None

    def _detect_format(self, detections: Any) -> str:
        """
        Automatically detect the format of detection results.

        Args:
            detections: Detection results in unknown format

        Returns:
            Detected format string
        """
        if isinstance(detections, list) and len(detections) > 0:
            first_det = detections[0]

            # Check for Hailo/Degirum format
            if isinstance(first_det, dict):
                if 'bbox' in first_det and 'score' in first_det:
                    return 'hailo'
                elif 'box' in first_det and 'confidence' in first_det:
                    return 'yolo'
                elif all(k in first_det for k in ('x1', 'y1', 'x2', 'y2')):
                    return 'generic'

            # Check for framework Detection objects
            elif hasattr(first_det, 'bbox') and hasattr(first_det, 'score'):
                return 'framework'

        # Check for object with results attribute (Degirum style)
        elif hasattr(detections, 'results'):
            return 'hailo'

        return 'generic'

    def _generate_cache_key(
        self,
        detections: Any,
        source_format: str,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]]
    ) -> str:
        """Generate a cache key for detection conversion results."""
        try:
            # Create a simple hash-based key
            det_hash = hash(str(detections)[:100])  # Limit string length for performance
            shape_hash = hash(frame_shape) if frame_shape else 0
            names_hash = hash(tuple(class_names)) if class_names else 0

            return f"{source_format}_{det_hash}_{shape_hash}_{names_hash}"
        except Exception:
            # Fallback to timestamp-based key if hashing fails
            return f"{source_format}_{time.time()}"

    def _convert_hailo_to_supervision(
        self,
        detections: Any,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert Hailo/Degirum detection format to supervision.Detections."""
        xyxy_list = []
        confidence_list = []
        class_id_list = []
        metadata = {}

        # Handle object with .results attribute (Degirum style)
        if hasattr(detections, 'results'):
            detection_list = detections.results
        elif isinstance(detections, list):
            detection_list = detections
        else:
            detection_list = [detections]

        for i, det in enumerate(detection_list):
            try:
                # Extract bounding box
                bbox = det.get('bbox') or det.get('box')
                if bbox is None or len(bbox) != 4:
                    continue

                # Extract confidence score
                score = det.get('score') or det.get('confidence', 0.0)

                # Convert bbox to xyxy format
                x1, y1, x2, y2 = bbox

                # Handle normalized coordinates
                if frame_shape and all(0 <= coord <= 1.05 for coord in bbox):
                    h, w = frame_shape
                    x1, x2 = x1 * w, x2 * w
                    y1, y2 = y1 * h, y2 * h

                # Handle xywh format (if x2 < x1 or y2 < y1)
                if x2 < x1 or y2 < y1:
                    x2, y2 = x1 + x2, y1 + y2

                xyxy_list.append([x1, y1, x2, y2])
                confidence_list.append(float(score))
                class_id_list.append(0)  # Default class ID

                # Preserve metadata if requested
                if preserve_metadata:
                    for key, value in det.items():
                        if key not in ('bbox', 'box', 'score', 'confidence'):
                            if key not in metadata:
                                metadata[key] = []
                            metadata[key].append(value)

            except Exception as e:
                logger.debug(f"Failed to parse detection {i}: {e}")
                continue

        if not xyxy_list:
            return sv.Detections.empty()

        # Create supervision detections
        xyxy = np.array(xyxy_list, dtype=np.float32)
        confidence = np.array(confidence_list, dtype=np.float32)
        class_id = np.array(class_id_list, dtype=int)

        # Add class names if provided
        if class_names:
            if len(class_names) == len(xyxy_list):
                metadata['class_name'] = np.array(class_names)
            else:
                metadata['class_name'] = np.array(['detection'] * len(xyxy_list))
        else:
            metadata['class_name'] = np.array(['detection'] * len(xyxy_list))

        # Ensure all metadata arrays have the same length as detections
        final_metadata = {}
        for key, values in metadata.items():
            if len(values) == len(xyxy_list):
                if isinstance(values, list) and len(values) > 0:
                    # Convert to numpy array with appropriate dtype
                    if isinstance(values[0], str):
                        final_metadata[key] = np.array(values, dtype=object)
                    else:
                        final_metadata[key] = np.array(values)
                else:
                    final_metadata[key] = values
            else:
                logger.debug(f"Skipping metadata key '{key}' due to length mismatch: {len(values)} vs {len(xyxy_list)}")

        return sv.Detections(
            xyxy=xyxy,
            confidence=confidence,
            class_id=class_id,
            data=final_metadata
        )

    def _convert_framework_detections_to_supervision(
        self,
        detections: List[Detection],
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert framework Detection objects to supervision.Detections."""
        if not detections:
            return sv.Detections.empty()

        xyxy_list = []
        confidence_list = []
        class_id_list = []
        metadata = {'class_name': []}

        for i, det in enumerate(detections):
            try:
                # Extract data from Detection object
                x1, y1, x2, y2 = det.bbox
                xyxy_list.append([x1, y1, x2, y2])
                confidence_list.append(det.score if det.score is not None else 0.0)
                class_id_list.append(0)  # Default class ID

                # Add class name
                class_name = class_names[i] if class_names and i < len(class_names) else det.label or 'detection'
                metadata['class_name'].append(class_name)

                # Preserve raw data if requested
                if preserve_metadata and det.raw is not None:
                    if 'raw_data' not in metadata:
                        metadata['raw_data'] = []
                    metadata['raw_data'].append(det.raw)

            except Exception as e:
                logger.debug(f"Failed to convert framework detection {i}: {e}")
                continue

        if not xyxy_list:
            return sv.Detections.empty()

        return sv.Detections(
            xyxy=np.array(xyxy_list, dtype=np.float32),
            confidence=np.array(confidence_list, dtype=np.float32),
            class_id=np.array(class_id_list, dtype=int),
            data={k: np.array(v) if isinstance(v[0], str) else v for k, v in metadata.items()}
        )

    def _convert_yolo_to_supervision(
        self,
        detections: Any,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert YOLO detection format to supervision.Detections."""
        # This is a placeholder for YOLO format conversion
        # Can be extended based on specific YOLO output format
        return self._convert_generic_to_supervision(detections, frame_shape, class_names, preserve_metadata)

    def _convert_generic_to_supervision(
        self,
        detections: Any,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert generic detection format to supervision.Detections using existing parsing logic."""
        try:
            # Use the existing framework parsing logic
            if frame_shape:
                h, w = frame_shape
                # Use BaseFrameProcessor methods for parsing
                processor = BaseFrameProcessor()
                raw_items = processor._explode_model_output(detections)
                parsed_detections = []

                for item in raw_items:
                    try:
                        bbox_raw, score, label = processor._extract_bbox_score_label(item)
                        if bbox_raw is None:
                            continue
                        x1, y1, x2, y2 = processor._to_xyxy_pixels(bbox_raw, w, h)
                        det = Detection(bbox=(x1, y1, x2, y2), score=score, label=label, raw=item)
                        parsed_detections.append(det)
                    except Exception as e:
                        logger.debug(f"Failed parsing generic detection item: {e}")
                        continue

                # Convert parsed detections to supervision format
                return self._convert_framework_detections_to_supervision(
                    parsed_detections, frame_shape, class_names, preserve_metadata
                )
            else:
                logger.warning("Frame shape required for generic detection conversion")
                return sv.Detections.empty()

        except Exception as e:
            logger.error(f"Failed to convert generic detections: {e}")
            return sv.Detections.empty()


class SupervisionTracker:
    """
    Intelligent tracking wrapper that provides easy integration with various tracking algorithms.

    This class wraps supervision's tracking capabilities and provides a clean interface
    for adding object tracking to any detection workflow. It supports multiple tracking
    algorithms and provides intelligent parameter tuning.
    """

    def __init__(
        self,
        tracker_type: str = 'bytetrack',
        track_activation_threshold: float = 0.25,
        lost_track_buffer: int = 30,
        minimum_matching_threshold: float = 0.8,
        frame_rate: int = 30,
        enable_smoothing: bool = False
    ):
        """
        Initialize the tracking system.

        Args:
            tracker_type: Type of tracker ('bytetrack', 'deepsort', etc.)
            track_activation_threshold: Minimum confidence to start a new track
            lost_track_buffer: Number of frames to keep lost tracks
            minimum_matching_threshold: Matching threshold for track association
            frame_rate: Video frame rate for temporal consistency
            enable_smoothing: Whether to enable detection smoothing
        """
        self.tracker_type = tracker_type
        self.enable_smoothing = enable_smoothing
        self._tracker = None
        self._smoother = None

        # Tracking parameters
        self.track_params = {
            'track_activation_threshold': track_activation_threshold,
            'lost_track_buffer': lost_track_buffer,
            'minimum_matching_threshold': minimum_matching_threshold,
            'frame_rate': frame_rate
        }

        # Initialize tracker if supervision is available
        if SUPERVISION_AVAILABLE:
            self._initialize_tracker()
        else:
            logger.warning("Supervision not available - tracking disabled")

    def _initialize_tracker(self):
        """Initialize the tracking algorithm."""
        try:
            if self.tracker_type.lower() == 'bytetrack':
                self._tracker = sv.ByteTrack(**self.track_params)
            else:
                logger.warning(f"Unsupported tracker type: {self.tracker_type}, using ByteTrack")
                self._tracker = sv.ByteTrack(**self.track_params)

            # Initialize smoother if requested
            if self.enable_smoothing:
                self._smoother = sv.DetectionsSmoother()

            logger.info(f"Initialized {self.tracker_type} tracker with smoothing={'enabled' if self.enable_smoothing else 'disabled'}")

        except Exception as e:
            logger.error(f"Failed to initialize tracker: {e}")
            self._tracker = None

    def is_available(self) -> bool:
        """Check if tracking is available."""
        return self._tracker is not None

    def update_with_detections(self, detections: "sv.Detections") -> "sv.Detections":
        """
        Update tracker with new detections.

        Args:
            detections: supervision.Detections object

        Returns:
            Updated detections with tracking IDs
        """
        if not self.is_available() or detections is None:
            return detections

        try:
            # Update tracker
            tracked_detections = self._tracker.update_with_detections(detections)

            # Apply smoothing if enabled
            if self._smoother is not None:
                tracked_detections = self._smoother.update_with_detections(tracked_detections)

            return tracked_detections

        except Exception as e:
            logger.error(f"Tracking update failed: {e}")
            return detections

    def reset(self):
        """Reset the tracker state."""
        if self._tracker is not None:
            try:
                # Re-initialize tracker to reset state
                self._initialize_tracker()
                logger.debug("Tracker reset successfully")
            except Exception as e:
                logger.error(f"Failed to reset tracker: {e}")


class EnhancedFrameProcessor(BaseFrameProcessor):
    """
    Enhanced FrameProcessor with built-in supervision integration and tracking support.

    This class extends the base FrameProcessor to provide seamless integration with
    the supervision library, including automatic format conversion, tracking, and
    professional visualization capabilities.
    """

    def __init__(
        self,
        conf_threshold: float = 0.0,
        enable_supervision: bool = True,
        enable_tracking: bool = False,
        tracker_config: Optional[Dict[str, Any]] = None,
        enable_caching: bool = True
    ):
        """
        Initialize the enhanced frame processor.

        Args:
            conf_threshold: Confidence threshold for detections
            enable_supervision: Whether to use supervision format and visualization
            enable_tracking: Whether to enable object tracking
            tracker_config: Configuration for the tracker
            enable_caching: Whether to enable conversion caching
        """
        super().__init__(conf_threshold)

        self.enable_supervision = enable_supervision and SUPERVISION_AVAILABLE
        self.enable_tracking = enable_tracking and SUPERVISION_AVAILABLE

        # Initialize supervision converter
        self.converter = SupervisionConverter(enable_caching=enable_caching) if self.enable_supervision else None

        # Initialize tracker
        self.tracker = None
        if self.enable_tracking:
            tracker_config = tracker_config or {}
            self.tracker = SupervisionTracker(**tracker_config)

        # Initialize supervision annotators
        self.box_annotator = None
        self.label_annotator = None
        if self.enable_supervision:
            try:
                self.box_annotator = sv.BoxAnnotator()
                self.label_annotator = sv.LabelAnnotator()
            except Exception as e:
                logger.warning(f"Failed to initialize supervision annotators: {e}")
                self.enable_supervision = False

        logger.info(f"EnhancedFrameProcessor initialized (supervision={'enabled' if self.enable_supervision else 'disabled'}, "
                   f"tracking={'enabled' if self.enable_tracking else 'disabled'})")

    def process(self, pkt: FramePacket) -> np.ndarray:
        """
        Process frame packet with automatic supervision integration.

        This method automatically uses supervision format and visualization if enabled,
        otherwise falls back to the original processing method.

        Args:
            pkt: FramePacket containing frame and model output

        Returns:
            Processed RGB frame with annotations
        """
        if self.enable_supervision:
            return self.process_with_supervision(pkt, source_format='auto')
        else:
            # Fallback to original processing
            detections = self.parse_detections(pkt.model_out, pkt.frame_rgb.shape[:2])
            return self.draw_boxes(pkt.frame_rgb, detections)

    def process_with_supervision(
        self,
        pkt: FramePacket,
        source_format: str = 'auto',
        class_names: Optional[List[str]] = None
    ) -> np.ndarray:
        """
        Process frame packet using supervision format and visualization.

        Args:
            pkt: FramePacket containing frame and model output
            source_format: Source format of detections
            class_names: Optional class names for detections

        Returns:
            Processed RGB frame with professional annotations
        """
        if not self.enable_supervision:
            # Fallback to original processing
            return self.process(pkt)

        try:
            h, w = pkt.frame_rgb.shape[:2]

            # Convert detections to supervision format
            sv_detections = self.converter.convert_to_supervision(
                pkt.model_out,
                source_format=source_format,
                frame_shape=(h, w),
                class_names=class_names
            )

            if sv_detections is None or len(sv_detections) == 0:
                return pkt.frame_rgb.copy()

            # Apply tracking if enabled
            if self.tracker and self.tracker.is_available():
                sv_detections = self.tracker.update_with_detections(sv_detections)

            # Create professional annotations
            annotated_frame = pkt.frame_rgb.copy()

            if self.box_annotator:
                annotated_frame = self.box_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections
                )

            if self.label_annotator:
                # Create labels with tracking IDs if available
                labels = self._create_labels(sv_detections)
                annotated_frame = self.label_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections,
                    labels=labels
                )

            return annotated_frame

        except Exception as e:
            logger.error(f"Supervision processing failed: {e}")
            # Fallback to original processing
            return self.process(pkt)

    def _create_labels(self, sv_detections: "sv.Detections") -> List[str]:
        """Create labels for detections including tracking IDs."""
        labels = []

        for i in range(len(sv_detections)):
            label_parts = []

            # Add tracking ID if available
            if hasattr(sv_detections, 'tracker_id') and sv_detections.tracker_id is not None:
                if i < len(sv_detections.tracker_id):
                    label_parts.append(f"#{sv_detections.tracker_id[i]}")

            # Add class name if available
            if 'class_name' in sv_detections.data and i < len(sv_detections.data['class_name']):
                label_parts.append(str(sv_detections.data['class_name'][i]))

            # Add confidence score
            if i < len(sv_detections.confidence):
                label_parts.append(f"{sv_detections.confidence[i]:.2f}")

            labels.append(" ".join(label_parts) if label_parts else "detection")

        return labels

    def get_supervision_detections(
        self,
        pkt: FramePacket,
        source_format: str = 'auto',
        class_names: Optional[List[str]] = None
    ) -> Optional["sv.Detections"]:
        """
        Get detections in supervision format without visualization.

        Args:
            pkt: FramePacket containing frame and model output
            source_format: Source format of detections
            class_names: Optional class names for detections

        Returns:
            supervision.Detections object or None
        """
        if not self.enable_supervision:
            return None

        try:
            h, w = pkt.frame_rgb.shape[:2]

            sv_detections = self.converter.convert_to_supervision(
                pkt.model_out,
                source_format=source_format,
                frame_shape=(h, w),
                class_names=class_names
            )

            # Apply tracking if enabled
            if self.tracker and self.tracker.is_available() and sv_detections is not None:
                sv_detections = self.tracker.update_with_detections(sv_detections)

            return sv_detections

        except Exception as e:
            logger.error(f"Failed to get supervision detections: {e}")
            return None
